'use client';

import { useState } from 'react';
import {
  MessageIcon,
  UserIcon,
  PackageIcon,
  LightningIcon,
  EmailIcon,
  PaymentIcon,
  RocketIcon,
  RefreshIcon,
  CheckIcon,
  QuestionIcon
} from '../components/icons';

export default function Home() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [stockStatus, setStockStatus] = useState({ sold: 11, available: 10 });
  const [serviceStatus] = useState('服务正常');
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  // 模拟库存刷新
  const refreshStock = () => {
    setIsLoading(true);
    setTimeout(() => {
      setStockStatus({
        sold: Math.floor(Math.random() * 20),
        available: Math.floor(Math.random() * 15) + 5
      });
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Animated gradient orbs */}
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 -right-4 w-72 h-72 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>
        
        {/* Radial gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 backdrop-blur-sm bg-white/5 border-b border-white/10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <span className="text-white font-bold text-lg">A</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white leading-tight">AugmentCode</h1>
                <p className="text-sm text-blue-200 leading-tight">专业AI编程助手租赁平台</p>
              </div>
            </div>
            <div className="px-3 py-1.5 bg-green-500/20 text-green-300 rounded-lg text-sm font-medium border border-green-400/30 backdrop-blur-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>{serviceStatus}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-6">
          <div className="max-w-6xl mx-auto text-center space-y-12">
            {/* Main Hero Content */}
            <div className="space-y-8">
              <div className="space-y-6 pt-16">
                <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight">
                  <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                    AugmentCode
                  </span>
                  <br />
                  <span className="text-white">账号租赁</span>
                </h1>
                
                <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
                  专业AI编程助手 · <span className="text-blue-300 font-semibold">650次消息/月</span> · 
                  <span className="text-emerald-300 font-semibold">独享账号</span> · 
                  <span className="text-purple-300 font-semibold">即买即用</span>
                </p>
                
                <p className="text-lg text-blue-200/80 max-w-3xl mx-auto">
                  成品号免配置，无需安装插件，支付成功立即使用
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300 hover:scale-105">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center space-x-2">
                    <RocketIcon className="w-5 h-5" />
                    <span>立即购买 ¥99</span>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">→</span>
                  </div>
                </button>
                
                <button
                  onClick={() => document.getElementById('faq-section')?.scrollIntoView({ behavior: 'smooth' })}
                  className="px-8 py-4 bg-white/10 text-white font-semibold rounded-xl border border-white/20 backdrop-blur-sm hover:bg-white/20 transition-all duration-300"
                >
                  <div className="flex items-center space-x-2">
                    <QuestionIcon className="w-5 h-5" />
                    <span>常见问题</span>
                  </div>
                </button>
              </div>

              {/* Official Price Comparison */}
              <div className="mt-16 max-w-6xl mx-auto">
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-2xl"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-400/20 to-pink-500/20 rounded-full blur-xl"></div>

                  <div className="relative z-10">
                    {/* Header with Price Comparison */}
                    <div className="text-center mb-8">
                      <div className="inline-flex items-center px-4 py-2 bg-yellow-500/20 border border-yellow-400/30 rounded-full text-yellow-300 text-sm font-medium backdrop-blur-sm mb-6">
                        <span className="mr-2">💰</span>
                        官方定价对比
                      </div>

                      {/* Price Comparison in Header */}
                      <div className="flex items-center justify-center space-x-8 mb-6">
                        {/* Original Price */}
                        <div className="text-center">
                          <div className="text-3xl font-bold text-red-400 line-through opacity-75">
                            $50/月
                          </div>
                          <div className="text-sm text-red-300">官方价格</div>
                        </div>

                        {/* Slash Animation */}
                        <div className="animate-slash text-5xl text-yellow-400 font-bold">
                          ⚡
                        </div>

                        {/* Our Price */}
                        <div className="text-center">
                          <div className="text-4xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                            ¥99
                          </div>
                          <div className="text-sm text-green-300">我们的价格</div>
                        </div>

                        {/* Savings Badge */}
                        <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-full backdrop-blur-sm animate-savings-pulse">
                          <span className="text-green-300 font-bold text-lg mr-2">🎉</span>
                          <span className="text-green-300 font-semibold">节省超过80%</span>
                        </div>
                      </div>

                      {/* Horizontal Value Points */}
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-2 md:gap-4 mb-8">
                        <div className="flex items-center justify-center space-x-1 md:space-x-2 text-blue-200 text-xs md:text-sm">
                          <span className="text-green-400">✓</span>
                          <span>650次/月消息额度</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1 md:space-x-2 text-blue-200 text-xs md:text-sm">
                          <span className="text-green-400">✓</span>
                          <span>独享账号，无需共享</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1 md:space-x-2 text-blue-200 text-xs md:text-sm">
                          <span className="text-green-400">✓</span>
                          <span>即买即用，无需等待</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1 md:space-x-2 text-blue-200 text-xs md:text-sm">
                          <span className="text-green-400">✓</span>
                          <span>7x24小时技术支持</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1 md:space-x-2 text-blue-200 text-xs md:text-sm">
                          <span className="text-green-400">✓</span>
                          <span>100%售后无忧</span>
                        </div>
                      </div>
                    </div>

                    {/* Large Official Price Image */}
                    <div className="max-w-4xl mx-auto">
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                        <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:scale-105 transition-transform duration-300">
                          <img
                            src="/price.jpg"
                            alt="AugmentCode官方定价"
                            className="w-full h-auto rounded-xl shadow-lg"
                          />
                        </div>
                      </div>
                      <div className="text-center mt-4">
                        <p className="text-white font-semibold text-lg mb-2">官方定价：$50美元/月</p>
                        <a
                          href="https://augmentcode.com/pricing"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center space-x-2 text-blue-300 hover:text-blue-200 transition-colors text-sm"
                        >
                          <span>🔗</span>
                          <span>官方价格直达</span>
                          <span>→</span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16">
              <div className="group p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <MessageIcon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">650次/月</h3>
                <p className="text-blue-200 text-sm">
                  充足的消息额度<br />满足日常开发需求
                </p>
              </div>

              <div className="group p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <UserIcon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">独享账号</h3>
                <p className="text-blue-200 text-sm">
                  专属个人使用<br />不与他人共享
                </p>
              </div>

              <div className="group p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <PackageIcon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">成品号</h3>
                <p className="text-blue-200 text-sm">
                  无需配置安装<br />开箱即用
                </p>
              </div>

              <div className="group p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <LightningIcon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">即时交付</h3>
                <p className="text-blue-200 text-sm">
                  支付成功后<br />立即发送至邮箱
                </p>
              </div>
            </div>

          </div>
        </section>

        {/* Purchase Section - Floating Card */}
        <section className="relative mt-16 px-6 pb-20">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-3xl p-8 shadow-2xl">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-white mb-4">开始购买 AugmentCode 账号</h2>
                <p className="text-blue-200">填写邮箱，完成支付，立即获得专属账号</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Side - Form */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-4 p-4 bg-white/5 rounded-2xl border border-white/10">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
                      <RocketIcon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white">立即购买</h3>
                      <p className="text-blue-200 text-sm">¥99 · 650次消息 · 独享账号</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="flex items-center space-x-2 text-sm font-medium text-blue-200 mb-2">
                        <EmailIcon className="w-4 h-4" />
                        <span>接收邮箱</span>
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="请输入您的邮箱地址"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all backdrop-blur-sm"
                        />
                      </div>
                      <p className="text-xs text-blue-300 mt-2 flex items-center space-x-1">
                        <div className="w-3 h-3 rounded-full bg-blue-400/20 flex items-center justify-center">
                          <span className="text-blue-300 text-xs">ℹ</span>
                        </div>
                        <span>账号验证码会发送至此邮箱</span>
                      </p>
                    </div>

                    <button
                      disabled={!email || isLoading || stockStatus.available === 0}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg shadow-blue-500/25"
                    >
                      <PaymentIcon className="w-5 h-5" />
                      <span>
                        {stockStatus.available === 0 
                          ? '检查库存中...' 
                          : !email 
                            ? '立即购买 ¥99' 
                            : '立即购买 ¥99'
                        }
                      </span>
                      {stockStatus.available > 0 && <span>→</span>}
                    </button>
                  </div>
                </div>

                {/* Right Side - Features */}
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-2xl border border-white/10">
                    <h4 className="text-lg font-semibold text-white mb-4">购买说明</h4>
                    <ul className="space-y-3 text-sm text-blue-200">
                      <li className="flex items-start space-x-2">
                        <CheckIcon className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span>支付成功后，账号信息将立即发送至您的邮箱</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckIcon className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span>650次/月消息额度，足够日常开发使用</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckIcon className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span>独享账号，不与他人共享，保证使用体验</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckIcon className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span>成品号免配置，收到即可立即使用</span>
                      </li>
                    </ul>
                  </div>

                  <div className="p-6 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl border border-green-400/20">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold text-white">实时库存</h4>
                      <button
                        onClick={refreshStock}
                        disabled={isLoading}
                        className="flex items-center space-x-1 px-3 py-1 bg-white/10 text-white rounded-lg hover:bg-white/20 disabled:opacity-50 transition-colors text-sm"
                      >
                        <RefreshIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                        <span>刷新</span>
                      </button>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{stockStatus.sold}</div>
                        <div className="text-blue-200 text-sm">已售出</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{stockStatus.available}</div>
                        <div className="text-blue-200 text-sm">库存充足</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq-section" className="relative px-6 py-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-6">
              <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 border border-blue-400/30 rounded-full text-blue-300 text-sm font-medium backdrop-blur-sm">
                <span className="mr-2">❓</span>
                常见问题
              </div>
            </div>

            <div className="space-y-4 mb-12">
              {/* FAQ Items */}
              {[
                {
                  question: "接收不到验证码怎么办？",
                  answer: (
                    <div>
                      <p className="text-blue-200 text-sm leading-relaxed mb-4">
                        请联系客服检查邮箱是否填写错误，我们的客服团队将为您提供专业帮助。
                      </p>
                      <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/20">
                        <p className="text-blue-300 text-sm">
                          <span className="font-medium">客服邮箱：</span>
                          <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                  )
                },
                {
                  question: "账号使用期限是多久？",
                  answer: (
                    <p className="text-blue-200 text-sm leading-relaxed">
                      账号有效期为一个月，650次消息额度在有效期内使用完毕。到期后可以重新购买续费。
                    </p>
                  )
                },
                {
                  question: "登录失败，报错怎么办？",
                  answer: (
                    <div>
                      <p className="text-blue-200 text-sm leading-relaxed mb-3">
                        如果遇到"Sign in failed. No OAuth state found"等错误，请尝试以下解决方案：
                      </p>
                      <ul className="text-blue-200 text-sm space-y-2 mb-4">
                        <li className="flex items-start space-x-2">
                          <span className="text-green-400 mt-1">•</span>
                          <span>切换网络环境重试</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-green-400 mt-1">•</span>
                          <span>开启或关闭 VPN</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-green-400 mt-1">•</span>
                          <span>使用清理工具清理后重试</span>
                        </li>
                      </ul>
                      <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/20">
                        <p className="text-blue-300 text-sm">
                          <span className="font-medium">清理工具地址：</span>
                          <a
                            href="https://pan.quark.cn/s/ed1203a70acd"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                          >
                            https://pan.quark.cn/s/ed1203a70acd
                          </a>
                        </p>
                      </div>
                    </div>
                  )
                },
                {
                  question: "支持退款吗？",
                  answer: (
                    <p className="text-blue-200 text-sm leading-relaxed">
                      由于是数字产品，一旦发送账号信息后不支持退款。购买前请确认您的需求和邮箱地址。
                    </p>
                  )
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-300">
                  <button
                    onClick={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                  >
                    <div className="flex items-center">
                      <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">Q</span>
                      <h3 className="text-lg font-semibold text-white">{faq.question}</h3>
                    </div>
                    <div className={`transform transition-transform duration-300 ${expandedFAQ === index ? 'rotate-180' : ''}`}>
                      <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${expandedFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                    <div className="px-6 pb-6">
                      {faq.answer}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Target Audience */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 border border-purple-400/30 rounded-full text-purple-300 text-sm font-medium backdrop-blur-sm mb-6">
                <span className="mr-2">👥</span>
                适用人群
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">谁适合使用 AugmentCode？</h2>
              <p className="text-blue-200">无论您是哪个阶段的开发者，都能从中受益</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="group text-center p-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">👨‍💻</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">开发者</h3>
                <p className="text-blue-200 text-sm leading-relaxed">
                  提升编程效率，智能代码补全，错误检测，代码优化，让开发工作更加高效便捷。
                </p>
                <div className="mt-4 flex flex-wrap justify-center gap-2">
                  <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">前端开发</span>
                  <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">后端开发</span>
                  <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">全栈开发</span>
                </div>
              </div>

              <div className="group text-center p-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🎓</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">学生</h3>
                <p className="text-blue-200 text-sm leading-relaxed">
                  学习编程的最佳助手，提供代码解释，学习指导，帮助快速掌握编程技能。
                </p>
                <div className="mt-4 flex flex-wrap justify-center gap-2">
                  <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">计算机专业</span>
                  <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">编程初学者</span>
                  <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">自学编程</span>
                </div>
              </div>

              <div className="group text-center p-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🏢</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">企业团队</h3>
                <p className="text-blue-200 text-sm leading-relaxed">
                  团队协作开发，提升整体开发效率，统一代码规范，加速项目交付进度。
                </p>
                <div className="mt-4 flex flex-wrap justify-center gap-2">
                  <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">初创公司</span>
                  <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">技术团队</span>
                  <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">外包团队</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="relative z-10 bg-black/20 backdrop-blur-sm border-t border-white/10 py-8">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <p className="text-blue-200 text-sm">
            © 2025 AugmentCode 账号租赁平台 · 专业AI编程助手服务
          </p>
        </div>
      </footer>
    </div>
  );
}
