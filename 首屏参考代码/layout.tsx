import type { Metadata } from "next";
import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AugmentCode账号租赁 - 专业AI编程助手 | 600次/月独享账号",
  description: "专业的AugmentCode账号租赁平台，600次消息/月，独享账号，成品号即买即用，无需安装插件，适合开发者和企业使用。微信支付，即时交付。",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
