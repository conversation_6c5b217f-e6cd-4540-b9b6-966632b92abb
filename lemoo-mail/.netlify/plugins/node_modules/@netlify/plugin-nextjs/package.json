{"name": "@netlify/plugin-nextjs", "version": "5.11.6", "description": "Run Next.js seamlessly on Netlify", "main": "./dist/index.js", "type": "module", "files": ["dist", "edge-runtime", "manifest.yml"], "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/opennextjs/opennextjs-netlify.git"}, "keywords": ["nextjs", "netlify", "next", "netlify-runtime"], "license": "MIT", "bugs": {"url": "https://github.com/opennextjs/opennextjs-netlify/issues"}, "homepage": "https://opennext.js.org/netlify"}