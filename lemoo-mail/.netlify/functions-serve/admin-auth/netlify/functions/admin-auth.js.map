{"version": 3, "sources": ["../../../../../../Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/netlify/functions/admin-auth.ts"], "sourceRoot": "/var/folders/j1/f2mj85rj7m3_qzr0scc0cxym0000gn/T/tmp-27234-8ApFRnhFyiON", "sourcesContent": ["import { Handler } from '@netlify/functions';\nimport { createHmac } from 'crypto';\n\n// 🔐 安全的管理员认证\nconst ADMIN_CREDENTIALS = {\n  username: process.env.ADMIN_USERNAME || 'admin',\n  password: process.env.ADMIN_PASSWORD || '', // 必须在环境变量中设置\n  secretKey: process.env.ADMIN_SECRET_KEY || '', // JWT密钥\n};\n\n// 简单的速率限制（内存存储，重启后重置）\nconst loginAttempts = new Map<string, { count: number; lastAttempt: number }>();\nconst MAX_ATTEMPTS = 5;\nconst LOCKOUT_TIME = 15 * 60 * 1000; // 15分钟\n\nfunction checkRateLimit(ip: string): boolean {\n  const now = Date.now();\n  const attempts = loginAttempts.get(ip);\n\n  if (!attempts) {\n    return true; // 首次尝试\n  }\n\n  // 如果锁定时间已过，重置计数\n  if (now - attempts.lastAttempt > LOCKOUT_TIME) {\n    loginAttempts.delete(ip);\n    return true;\n  }\n\n  // 检查是否超过最大尝试次数\n  return attempts.count < MAX_ATTEMPTS;\n}\n\nfunction recordFailedAttempt(ip: string): void {\n  const now = Date.now();\n  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };\n\n  attempts.count += 1;\n  attempts.lastAttempt = now;\n  loginAttempts.set(ip, attempts);\n}\n\nfunction clearFailedAttempts(ip: string): void {\n  loginAttempts.delete(ip);\n}\n\n// 🔐 安全的JWT实现\nfunction createToken(payload: any): string {\n  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');\n  const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64url');\n\n  // 使用HMAC-SHA256进行安全签名\n  const signature = createHmac('sha256', ADMIN_CREDENTIALS.secretKey)\n    .update(`${header}.${payloadStr}`)\n    .digest('base64url');\n\n  return `${header}.${payloadStr}.${signature}`;\n}\n\nfunction verifyToken(token: string): any {\n  try {\n    const [header, payload, signature] = token.split('.');\n\n    // 使用HMAC-SHA256验证签名\n    const expectedSignature = createHmac('sha256', ADMIN_CREDENTIALS.secretKey)\n      .update(`${header}.${payload}`)\n      .digest('base64url');\n\n    if (signature !== expectedSignature) {\n      return null;\n    }\n\n    const decodedPayload = JSON.parse(Buffer.from(payload, 'base64url').toString());\n\n    // 检查过期时间\n    if (decodedPayload.exp && Date.now() > decodedPayload.exp) {\n      return null;\n    }\n\n    return decodedPayload;\n  } catch {\n    return null;\n  }\n}\n\nexport const handler: Handler = async (event, context) => {\n  // 设置CORS头\n  const headers = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n    'Content-Type': 'application/json',\n  };\n\n  // 处理OPTIONS请求\n  if (event.httpMethod === 'OPTIONS') {\n    return {\n      statusCode: 200,\n      headers,\n      body: '',\n    };\n  }\n\n  try {\n    if (event.httpMethod === 'POST') {\n      // 管理员登录\n      const { username, password } = JSON.parse(event.body || '{}');\n\n      // 🔐 速率限制检查\n      const clientIP = event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown';\n      if (!checkRateLimit(clientIP)) {\n        return {\n          statusCode: 429,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '登录尝试过于频繁，请15分钟后再试',\n          }),\n        };\n      }\n\n      // 🔐 输入验证\n      if (!username || !password) {\n        return {\n          statusCode: 400,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '用户名和密码不能为空',\n          }),\n        };\n      }\n\n      // 限制输入长度，防止DoS攻击\n      if (username.length > 50 || password.length > 100) {\n        return {\n          statusCode: 400,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '输入长度超出限制',\n          }),\n        };\n      }\n\n      // 验证环境变量是否设置\n      if (!ADMIN_CREDENTIALS.password || !ADMIN_CREDENTIALS.secretKey) {\n        return {\n          statusCode: 500,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '服务器配置错误：管理员凭据未设置',\n          }),\n        };\n      }\n      \n      // 验证凭据\n      if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {\n        // 🔐 登录成功，清除失败记录\n        clearFailedAttempts(clientIP);\n\n        const token = createToken({\n          username,\n          role: 'admin',\n          exp: Date.now() + 24 * 60 * 60 * 1000, // 24小时过期\n        });\n\n        return {\n          statusCode: 200,\n          headers,\n          body: JSON.stringify({\n            success: true,\n            token,\n            message: '管理员登录成功',\n          }),\n        };\n      } else {\n        // 🔐 登录失败，记录尝试\n        recordFailedAttempt(clientIP);\n\n        return {\n          statusCode: 401,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '用户名或密码错误',\n          }),\n        };\n      }\n    }\n    \n    if (event.httpMethod === 'GET') {\n      // 验证管理员token\n      const authHeader = event.headers.authorization || event.headers.Authorization;\n      if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        return {\n          statusCode: 401,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: '缺少认证token',\n          }),\n        };\n      }\n      \n      const token = authHeader.substring(7);\n      const payload = verifyToken(token);\n      \n      if (!payload || payload.role !== 'admin') {\n        return {\n          statusCode: 401,\n          headers,\n          body: JSON.stringify({\n            success: false,\n            message: 'token无效或已过期',\n          }),\n        };\n      }\n      \n      return {\n        statusCode: 200,\n        headers,\n        body: JSON.stringify({\n          success: true,\n          user: {\n            username: payload.username,\n            role: payload.role,\n          },\n        }),\n      };\n    }\n    \n    return {\n      statusCode: 405,\n      headers,\n      body: JSON.stringify({\n        success: false,\n        message: '不支持的请求方法',\n      }),\n    };\n    \n  } catch (error) {\n    console.error('管理员认证错误:', error);\n    return {\n      statusCode: 500,\n      headers,\n      body: JSON.stringify({\n        success: false,\n        message: '服务器内部错误',\n      }),\n    };\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAG3B,IAAM,oBAAoB;AAAA,EACxB,UAAU,QAAQ,IAAI,kBAAkB;AAAA,EACxC,UAAU,QAAQ,IAAI,kBAAkB;AAAA;AAAA,EACxC,WAAW,QAAQ,IAAI,oBAAoB;AAAA;AAC7C;AAGA,IAAM,gBAAgB,oBAAI,IAAoD;AAC9E,IAAM,eAAe;AACrB,IAAM,eAAe,KAAK,KAAK;AAE/B,SAAS,eAAe,IAAqB;AAC3C,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,WAAW,cAAc,IAAI,EAAE;AAErC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,SAAS,cAAc,cAAc;AAC7C,kBAAc,OAAO,EAAE;AACvB,WAAO;AAAA,EACT;AAGA,SAAO,SAAS,QAAQ;AAC1B;AAEA,SAAS,oBAAoB,IAAkB;AAC7C,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,WAAW,cAAc,IAAI,EAAE,KAAK,EAAE,OAAO,GAAG,aAAa,EAAE;AAErE,WAAS,SAAS;AAClB,WAAS,cAAc;AACvB,gBAAc,IAAI,IAAI,QAAQ;AAChC;AAEA,SAAS,oBAAoB,IAAkB;AAC7C,gBAAc,OAAO,EAAE;AACzB;AAGA,SAAS,YAAY,SAAsB;AACzC,QAAM,SAAS,OAAO,KAAK,KAAK,UAAU,EAAE,KAAK,SAAS,KAAK,MAAM,CAAC,CAAC,EAAE,SAAS,WAAW;AAC7F,QAAM,aAAa,OAAO,KAAK,KAAK,UAAU,OAAO,CAAC,EAAE,SAAS,WAAW;AAG5E,QAAM,gBAAY,0BAAW,UAAU,kBAAkB,SAAS,EAC/D,OAAO,GAAG,MAAM,IAAI,UAAU,EAAE,EAChC,OAAO,WAAW;AAErB,SAAO,GAAG,MAAM,IAAI,UAAU,IAAI,SAAS;AAC7C;AAEA,SAAS,YAAY,OAAoB;AACvC,MAAI;AACF,UAAM,CAAC,QAAQ,SAAS,SAAS,IAAI,MAAM,MAAM,GAAG;AAGpD,UAAM,wBAAoB,0BAAW,UAAU,kBAAkB,SAAS,EACvE,OAAO,GAAG,MAAM,IAAI,OAAO,EAAE,EAC7B,OAAO,WAAW;AAErB,QAAI,cAAc,mBAAmB;AACnC,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,KAAK,MAAM,OAAO,KAAK,SAAS,WAAW,EAAE,SAAS,CAAC;AAG9E,QAAI,eAAe,OAAO,KAAK,IAAI,IAAI,eAAe,KAAK;AACzD,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,IAAM,UAAmB,OAAO,OAAO,YAAY;AAExD,QAAM,UAAU;AAAA,IACd,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,gBAAgB;AAAA,EAClB;AAGA,MAAI,MAAM,eAAe,WAAW;AAClC,WAAO;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI;AACF,QAAI,MAAM,eAAe,QAAQ;AAE/B,YAAM,EAAE,UAAU,SAAS,IAAI,KAAK,MAAM,MAAM,QAAQ,IAAI;AAG5D,YAAM,WAAW,MAAM,QAAQ,iBAAiB,KAAK,MAAM,QAAQ,WAAW,KAAK;AACnF,UAAI,CAAC,eAAe,QAAQ,GAAG;AAC7B,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,CAAC,YAAY,CAAC,UAAU;AAC1B,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,SAAS,SAAS,MAAM,SAAS,SAAS,KAAK;AACjD,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,CAAC,kBAAkB,YAAY,CAAC,kBAAkB,WAAW;AAC/D,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,aAAa,kBAAkB,YAAY,aAAa,kBAAkB,UAAU;AAEtF,4BAAoB,QAAQ;AAE5B,cAAM,QAAQ,YAAY;AAAA,UACxB;AAAA,UACA,MAAM;AAAA,UACN,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,QACnC,CAAC;AAED,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT;AAAA,YACA,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AAEL,4BAAoB,QAAQ;AAE5B,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,QAAI,MAAM,eAAe,OAAO;AAE9B,YAAM,aAAa,MAAM,QAAQ,iBAAiB,MAAM,QAAQ;AAChE,UAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,YAAM,UAAU,YAAY,KAAK;AAEjC,UAAI,CAAC,WAAW,QAAQ,SAAS,SAAS;AACxC,eAAO;AAAA,UACL,YAAY;AAAA,UACZ;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,UAAU,QAAQ;AAAA,YAClB,MAAM,QAAQ;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,+CAAY,KAAK;AAC/B,WAAO;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}