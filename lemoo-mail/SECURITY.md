# 🔐 LemooMail 安全指南

## ⚠️ 重要安全修复

本项目已修复以下严重安全漏洞：

### 1. 管理员认证漏洞（已修复）

**之前的问题：**
- ❌ 任何人都可以直接访问 `/admin` 页面
- ❌ 管理员权限仅基于邮箱名称包含"admin"
- ❌ 没有真正的身份验证机制

**现在的解决方案：**
- ✅ 独立的管理员登录页面 `/admin-login`
- ✅ 基于JWT的安全认证
- ✅ 环境变量存储管理员凭据
- ✅ Token过期机制（24小时）

### 2. 敏感信息泄露（已修复）

**之前的问题：**
- ❌ 163邮箱账号硬编码在客户端代码中
- ❌ 构建后的静态文件包含敏感信息
- ❌ 任何人都能查看源码获取邮箱信息

**现在的解决方案：**
- ✅ 敏感信息移至Netlify Functions
- ✅ 使用环境变量保护配置
- ✅ 客户端代码不包含任何敏感信息

### 3. 邮件服务架构（重新设计）

**之前的问题：**
- ❌ 完全使用模拟数据
- ❌ 无法连接真实的163邮箱
- ❌ 静态部署无法直接访问IMAP

**现在的解决方案：**
- ✅ Netlify Functions作为安全代理
- ✅ 服务端处理IMAP连接
- ✅ 客户端通过API安全获取数据

## 🛡️ 安全最佳实践

### 环境变量安全

1. **绝对不要**将敏感信息提交到Git仓库
2. **使用强密码**：管理员密码至少12位，包含大小写字母、数字、特殊字符
3. **定期更换**：定期更换管理员密码和JWT密钥
4. **权限最小化**：只给必要的人员提供管理员凭据

### 🔧 安全配置生成工具

使用内置工具生成安全配置：

```bash
# 生成强密码和JWT密钥
node scripts/generate-secrets.js

# 运行安全检查
node scripts/security-check.js
```

### 部署安全

1. **环境变量配置**：
   ```bash
   # 在Netlify项目设置中配置
   ADMIN_USERNAME=admin
   ADMIN_PASSWORD=your-secure-password-here
   ADMIN_SECRET_KEY=your-random-32-char-key
   IMAP_USER=<EMAIL>
   IMAP_PASSWORD=your-163-auth-code
   ```

2. **域名安全**：
   - 使用HTTPS（Netlify自动提供）
   - 配置适当的CORS策略
   - 考虑添加域名白名单

3. **访问控制**：
   - 管理员登录页面：`/admin-login`
   - 普通用户登录页面：`/login`
   - 管理面板需要认证：`/admin`

### 监控和审计

1. **日志监控**：
   - 监控管理员登录尝试
   - 记录用户创建和删除操作
   - 关注异常的API调用

2. **定期检查**：
   - 检查环境变量配置
   - 验证管理员账户安全
   - 更新依赖包版本

## 🚨 应急响应

如果发现安全问题：

1. **立即行动**：
   - 更改所有密码和密钥
   - 检查访问日志
   - 暂时禁用受影响的功能

2. **联系支持**：
   - 报告安全问题
   - 获取专业建议
   - 实施修复措施

## 📋 安全检查清单

部署前请确认：

- [ ] 所有环境变量已正确配置
- [ ] 管理员密码足够强壮
- [ ] JWT密钥是随机生成的
- [ ] 163邮箱授权码已设置
- [ ] 没有敏感信息在代码中硬编码
- [ ] 管理员登录功能正常工作
- [ ] 普通用户无法访问管理功能
- [ ] HTTPS已启用
- [ ] 域名配置正确

## 🔄 安全更新

本项目会持续改进安全性：

- 定期更新依赖包
- 修复发现的安全漏洞
- 改进认证机制
- 增强监控能力

---

**记住：安全是一个持续的过程，不是一次性的任务！**
