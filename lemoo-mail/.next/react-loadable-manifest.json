{"app/admin/page.tsx -> @/lib/database": {"id": "app/admin/page.tsx -> @/lib/database", "files": ["static/chunks/_app-pages-browser_src_lib_database_ts.js"]}, "app/page.tsx -> @/lib/database": {"id": "app/page.tsx -> @/lib/database", "files": ["static/chunks/_app-pages-browser_src_lib_database_ts.js"]}, "store/admin.ts -> @/lib/database": {"id": "store/admin.ts -> @/lib/database", "files": ["static/chunks/_app-pages-browser_src_lib_database_ts.js"]}, "store/auth.ts -> @/lib/database": {"id": "store/auth.ts -> @/lib/database", "files": ["static/chunks/_app-pages-browser_src_lib_database_ts.js"]}}