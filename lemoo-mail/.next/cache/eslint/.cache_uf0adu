[{"/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/admin/page.tsx": "1", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/layout.tsx": "2", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/login/page.tsx": "3", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/mail/page.tsx": "4", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/page.tsx": "5", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/EmailDetail.tsx": "6", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/EmailList.tsx": "7", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/Sidebar.tsx": "8", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/button.tsx": "9", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/card.tsx": "10", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/input.tsx": "11", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/auth.ts": "12", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/database.ts": "13", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/mail-service.ts": "14", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/utils.ts": "15", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/admin.ts": "16", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/auth.ts": "17", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/mail.ts": "18", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/types/index.ts": "19", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/admin-login/page.tsx": "20", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/marketing/MarketingCard.tsx": "21", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/password-input.tsx": "22", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/badge.tsx": "23", "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/supabase.ts": "24"}, {"size": 52406, "mtime": 1753860988906, "results": "25", "hashOfConfig": "26"}, {"size": 689, "mtime": 1753801252584, "results": "27", "hashOfConfig": "26"}, {"size": 8292, "mtime": 1753856102107, "results": "28", "hashOfConfig": "26"}, {"size": 17574, "mtime": 1753871614184, "results": "29", "hashOfConfig": "26"}, {"size": 4051, "mtime": 1753849256115, "results": "30", "hashOfConfig": "26"}, {"size": 6689, "mtime": 1753803245851, "results": "31", "hashOfConfig": "26"}, {"size": 6520, "mtime": 1753803214409, "results": "32", "hashOfConfig": "26"}, {"size": 7463, "mtime": 1753803283260, "results": "33", "hashOfConfig": "26"}, {"size": 1729, "mtime": 1753802810934, "results": "34", "hashOfConfig": "26"}, {"size": 1876, "mtime": 1753802830481, "results": "35", "hashOfConfig": "26"}, {"size": 807, "mtime": 1753803889732, "results": "36", "hashOfConfig": "26"}, {"size": 4834, "mtime": 1753803581757, "results": "37", "hashOfConfig": "26"}, {"size": 9255, "mtime": 1753852228462, "results": "38", "hashOfConfig": "26"}, {"size": 7057, "mtime": 1753839916305, "results": "39", "hashOfConfig": "26"}, {"size": 5063, "mtime": 1753845616051, "results": "40", "hashOfConfig": "26"}, {"size": 4835, "mtime": 1753845730472, "results": "41", "hashOfConfig": "26"}, {"size": 2781, "mtime": 1753833742443, "results": "42", "hashOfConfig": "26"}, {"size": 5886, "mtime": 1753852379462, "results": "43", "hashOfConfig": "26"}, {"size": 2583, "mtime": 1753852184729, "results": "44", "hashOfConfig": "26"}, {"size": 4617, "mtime": 1753849549857, "results": "45", "hashOfConfig": "26"}, {"size": 1523, "mtime": 1753845934620, "results": "46", "hashOfConfig": "26"}, {"size": 2271, "mtime": 1753849480983, "results": "47", "hashOfConfig": "26"}, {"size": 1128, "mtime": 1753852486879, "results": "48", "hashOfConfig": "26"}, {"size": 8005, "mtime": 1753852568867, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "15uks53", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/admin/page.tsx", ["122", "123", "124", "125"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/layout.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/login/page.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/mail/page.tsx", ["126", "127"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/page.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/EmailDetail.tsx", ["128"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/EmailList.tsx", ["129"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/mail/Sidebar.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/button.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/card.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/input.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/auth.ts", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/database.ts", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/mail-service.ts", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/utils.ts", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/admin.ts", ["130", "131", "132", "133", "134"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/auth.ts", ["135", "136"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/store/mail.ts", ["137"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/types/index.ts", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/app/admin-login/page.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/marketing/MarketingCard.tsx", ["138"], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/password-input.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/components/ui/badge.tsx", [], [], "/Users/<USER>/develop/augment-email/LemooMail/lemoo-mail/src/lib/supabase.ts", [], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 24, "column": 5, "nodeType": null, "messageId": "141", "endLine": 24, "endColumn": 15}, {"ruleId": "142", "severity": 1, "message": "143", "line": 128, "column": 6, "nodeType": "144", "endLine": 128, "endColumn": 42, "suggestions": "145"}, {"ruleId": "146", "severity": 2, "message": "147", "line": 746, "column": 63, "nodeType": "148", "messageId": "149", "suggestions": "150"}, {"ruleId": "146", "severity": 2, "message": "147", "line": 746, "column": 68, "nodeType": "148", "messageId": "149", "suggestions": "151"}, {"ruleId": "139", "severity": 1, "message": "152", "line": 20, "column": 10, "nodeType": null, "messageId": "141", "endLine": 20, "endColumn": 24}, {"ruleId": "142", "severity": 1, "message": "153", "line": 87, "column": 6, "nodeType": "144", "endLine": 87, "endColumn": 42, "suggestions": "154"}, {"ruleId": "139", "severity": 1, "message": "155", "line": 4, "column": 10, "nodeType": null, "messageId": "141", "endLine": 4, "endColumn": 20}, {"ruleId": "139", "severity": 1, "message": "156", "line": 11, "column": 37, "nodeType": null, "messageId": "141", "endLine": 11, "endColumn": 46}, {"ruleId": "139", "severity": 1, "message": "157", "line": 24, "column": 55, "nodeType": null, "messageId": "141", "endLine": 24, "endColumn": 58}, {"ruleId": "139", "severity": 1, "message": "158", "line": 85, "column": 14, "nodeType": null, "messageId": "141", "endLine": 85, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "158", "line": 113, "column": 14, "nodeType": null, "messageId": "141", "endLine": 113, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "158", "line": 143, "column": 14, "nodeType": null, "messageId": "141", "endLine": 143, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "158", "line": 176, "column": 14, "nodeType": null, "messageId": "141", "endLine": 176, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "157", "line": 27, "column": 11, "nodeType": null, "messageId": "141", "endLine": 27, "endColumn": 14}, {"ruleId": "139", "severity": 1, "message": "158", "line": 63, "column": 18, "nodeType": null, "messageId": "141", "endLine": 63, "endColumn": 23}, {"ruleId": "139", "severity": 1, "message": "158", "line": 86, "column": 14, "nodeType": null, "messageId": "141", "endLine": 86, "endColumn": 19}, {"ruleId": "159", "severity": 1, "message": "160", "line": 27, "column": 11, "nodeType": "161", "endLine": 32, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'clearError' is assigned a value but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'hasUserClearedDomain'. Either include it or remove the dependency array.", "ArrayExpression", ["162"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["163", "164", "165", "166"], ["167", "168", "169", "170"], "'lastEmailCount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isCheckingAuth'. Either include it or remove the dependency array.", ["171"], "'formatDate' is defined but never used.", "'userEmail' is defined but never used.", "'get' is defined but never used.", "'error' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"desc": "172", "fix": "173"}, {"messageId": "174", "data": "175", "fix": "176", "desc": "177"}, {"messageId": "174", "data": "178", "fix": "179", "desc": "180"}, {"messageId": "174", "data": "181", "fix": "182", "desc": "183"}, {"messageId": "174", "data": "184", "fix": "185", "desc": "186"}, {"messageId": "174", "data": "187", "fix": "188", "desc": "177"}, {"messageId": "174", "data": "189", "fix": "190", "desc": "180"}, {"messageId": "174", "data": "191", "fix": "192", "desc": "183"}, {"messageId": "174", "data": "193", "fix": "194", "desc": "186"}, {"desc": "195", "fix": "196"}, "Update the dependencies array to be: [isAuthenticated, createForm.domain, hasUserClearedDomain]", {"range": "197", "text": "198"}, "replaceWithAlt", {"alt": "199"}, {"range": "200", "text": "201"}, "Replace with `&quot;`.", {"alt": "202"}, {"range": "203", "text": "204"}, "Replace with `&ldquo;`.", {"alt": "205"}, {"range": "206", "text": "207"}, "Replace with `&#34;`.", {"alt": "208"}, {"range": "209", "text": "210"}, "Replace with `&rdquo;`.", {"alt": "199"}, {"range": "211", "text": "212"}, {"alt": "202"}, {"range": "213", "text": "214"}, {"alt": "205"}, {"range": "215", "text": "216"}, {"alt": "208"}, {"range": "217", "text": "218"}, "Update the dependencies array to be: [isAuthenticated, user, fetchEmails, isCheckingAuth]", {"range": "219", "text": "220"}, [3577, 3613], "[isAuthenticated, createForm.domain, hasUserClearedDomain]", "&quot;", [32967, 32984], "点击&quot;添加卡片\"创建第一个营销内容", "&ldquo;", [32967, 32984], "点击&ldquo;添加卡片\"创建第一个营销内容", "&#34;", [32967, 32984], "点击&#34;添加卡片\"创建第一个营销内容", "&rdquo;", [32967, 32984], "点击&rdquo;添加卡片\"创建第一个营销内容", [32967, 32984], "点击\"添加卡片&quot;创建第一个营销内容", [32967, 32984], "点击\"添加卡片&ldquo;创建第一个营销内容", [32967, 32984], "点击\"添加卡片&#34;创建第一个营销内容", [32967, 32984], "点击\"添加卡片&rdquo;创建第一个营销内容", [2614, 2650], "[isAuthenticated, user, fetchEmails, isCheckingAuth]"]