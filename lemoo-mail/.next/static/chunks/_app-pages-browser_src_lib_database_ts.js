"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_database_ts"],{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n// 本地存储键名\nconst DB_KEY = 'lemoo-mail-db';\n// 数据库服务类\nclass DatabaseService {\n    // 从本地存储加载数据\n    loadFromStorage() {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(DB_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // 确保日期字段正确解析\n                parsed.users = parsed.users.map((user)=>({\n                        ...user,\n                        createdAt: new Date(user.createdAt)\n                    }));\n                // 确保营销卡片字段存在\n                if (!parsed.marketingCards) {\n                    parsed.marketingCards = [];\n                } else {\n                    parsed.marketingCards = parsed.marketingCards.map((card)=>({\n                            ...card,\n                            createdAt: new Date(card.createdAt),\n                            updatedAt: new Date(card.updatedAt)\n                        }));\n                }\n                return parsed;\n            }\n        } catch (error) {\n            console.error('加载数据失败:', error);\n        }\n        return {\n            users: [],\n            marketingCards: []\n        };\n    }\n    // 保存数据到本地存储\n    saveToStorage() {\n        if (false) {}\n        try {\n            localStorage.setItem(DB_KEY, JSON.stringify(this.data));\n        } catch (error) {\n            console.error('保存数据失败:', error);\n        }\n    }\n    // 创建虚拟用户（使用随机用户名）\n    async createUsers(domain, count) {\n        const newUsers = [];\n        const maxAttempts = count * 3; // 最多尝试次数，避免无限循环\n        let attempts = 0;\n        while(newUsers.length < count && attempts < maxAttempts){\n            attempts++;\n            // 生成随机用户名\n            const username = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateRandomUsername)();\n            const email = \"\".concat(username, \"@\").concat(domain);\n            // 检查邮箱是否已存在\n            const existingUser = this.data.users.find((user)=>user.email === email);\n            if (existingUser) {\n                continue; // 跳过已存在的邮箱，重新生成\n            }\n            const user = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                email,\n                password: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateRandomPassword)(8),\n                createdAt: new Date(),\n                isActive: false\n            };\n            newUsers.push(user);\n            this.data.users.push(user);\n        }\n        this.saveToStorage();\n        return newUsers;\n    }\n    // 获取所有用户\n    async getAllUsers() {\n        return [\n            ...this.data.users\n        ];\n    }\n    // 根据邮箱获取用户\n    async getUserByEmail(email) {\n        return this.data.users.find((user)=>user.email === email) || null;\n    }\n    // 根据ID获取用户\n    async getUserById(id) {\n        return this.data.users.find((user)=>user.id === id) || null;\n    }\n    // 验证用户登录\n    async validateUser(email, password) {\n        const user = await this.getUserByEmail(email);\n        if (!user || !user.isActive) {\n            return null;\n        }\n        if (user.password === password) {\n            return user;\n        }\n        return null;\n    }\n    // 删除用户\n    async deleteUser(id) {\n        const index = this.data.users.findIndex((user)=>user.id === id);\n        if (index === -1) {\n            return false;\n        }\n        this.data.users.splice(index, 1);\n        this.saveToStorage();\n        return true;\n    }\n    // 切换用户状态\n    async toggleUserStatus(id) {\n        const user = this.data.users.find((user)=>user.id === id);\n        if (!user) {\n            return null;\n        }\n        user.isActive = !user.isActive;\n        // 如果是激活操作，记录激活时间\n        if (user.isActive) {\n            user.activatedAt = new Date();\n        }\n        this.saveToStorage();\n        return user;\n    }\n    // 更新用户信息\n    async updateUser(id, updates) {\n        const user = this.data.users.find((user)=>user.id === id);\n        if (!user) {\n            return null;\n        }\n        Object.assign(user, updates);\n        this.saveToStorage();\n        return user;\n    }\n    // 获取统计信息\n    async getStats() {\n        const totalUsers = this.data.users.length;\n        const activeUsers = this.data.users.filter((user)=>user.isActive).length;\n        const domains = [\n            ...new Set(this.data.users.map((user)=>user.email.split('@')[1]))\n        ];\n        return {\n            totalUsers,\n            activeUsers,\n            domains\n        };\n    }\n    // 清空所有数据\n    async clearAll() {\n        this.data = {\n            users: [],\n            marketingCards: []\n        };\n        this.saveToStorage();\n    }\n    // 导出数据\n    async exportData() {\n        return JSON.stringify(this.data, null, 2);\n    }\n    // 导入数据\n    async importData(jsonData) {\n        try {\n            var _imported_marketingCards;\n            const imported = JSON.parse(jsonData);\n            // 验证数据格式\n            if (!imported.users || !Array.isArray(imported.users)) {\n                throw new Error('无效的数据格式');\n            }\n            // 验证用户数据\n            for (const user of imported.users){\n                if (!user.id || !user.email || !user.password) {\n                    throw new Error('用户数据不完整');\n                }\n            }\n            this.data = {\n                users: imported.users.map((user)=>({\n                        ...user,\n                        createdAt: new Date(user.createdAt)\n                    })),\n                marketingCards: ((_imported_marketingCards = imported.marketingCards) === null || _imported_marketingCards === void 0 ? void 0 : _imported_marketingCards.map((card)=>({\n                        ...card,\n                        createdAt: new Date(card.createdAt),\n                        updatedAt: new Date(card.updatedAt)\n                    }))) || []\n            };\n            this.saveToStorage();\n            return true;\n        } catch (error) {\n            console.error('导入数据失败:', error);\n            return false;\n        }\n    }\n    // ==================== 营销卡片管理 ====================\n    // 获取所有营销卡片\n    async getAllMarketingCards() {\n        return [\n            ...this.data.marketingCards\n        ].sort((a, b)=>a.order - b.order);\n    }\n    // 获取激活的营销卡片\n    async getActiveMarketingCards() {\n        return this.data.marketingCards.filter((card)=>card.isActive).sort((a, b)=>a.order - b.order);\n    }\n    // 根据ID获取营销卡片\n    async getMarketingCardById(id) {\n        return this.data.marketingCards.find((card)=>card.id === id) || null;\n    }\n    // 创建营销卡片\n    async createMarketingCard(cardData) {\n        const now = new Date();\n        const newCard = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n            ...cardData,\n            createdAt: now,\n            updatedAt: now\n        };\n        this.data.marketingCards.push(newCard);\n        this.saveToStorage();\n        return newCard;\n    }\n    // 更新营销卡片\n    async updateMarketingCard(id, updates) {\n        const card = this.data.marketingCards.find((c)=>c.id === id);\n        if (!card) {\n            return null;\n        }\n        Object.assign(card, updates, {\n            updatedAt: new Date()\n        });\n        this.saveToStorage();\n        return card;\n    }\n    // 删除营销卡片\n    async deleteMarketingCard(id) {\n        const index = this.data.marketingCards.findIndex((card)=>card.id === id);\n        if (index === -1) {\n            return false;\n        }\n        this.data.marketingCards.splice(index, 1);\n        this.saveToStorage();\n        return true;\n    }\n    // 初始化默认营销卡片\n    async initializeDefaultMarketingCards() {\n        if (this.data.marketingCards.length === 0) {\n            const defaultCard = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                title: '🚀 LemooMail - 轻量级邮件管理系统',\n                description: '基于 Cloudflare Catch-All 转发的虚拟邮箱解决方案，零服务器成本，现代化技术栈，完美支持域名邮箱管理。无需复杂配置，一键部署即可使用。',\n                imageUrl: '',\n                buttonText: '立即开始使用',\n                buttonLink: '/login',\n                isActive: true,\n                order: 1,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            this.data.marketingCards.push(defaultCard);\n            this.saveToStorage();\n            console.log('默认营销卡片初始化完成');\n        }\n    }\n    // 初始化示例数据（仅初始化营销卡片，不创建测试用户）\n    async initSampleData() {\n        // 只初始化默认营销卡片，不创建测试用户\n        await this.initializeDefaultMarketingCards();\n        console.log('默认配置初始化完成');\n    }\n    constructor(){\n        this.data = this.loadFromStorage();\n    }\n}\n// 创建数据库服务实例\nconst db = new DatabaseService();\n// 在客户端初始化示例数据\nif (true) {\n    db.initSampleData().catch(console.error);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

}]);