"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_database_ts";
exports.ids = ["_ssr_src_lib_database_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n// 本地存储键名\nconst DB_KEY = 'lemoo-mail-db';\n// 数据库服务类\nclass DatabaseService {\n    constructor(){\n        this.data = this.loadFromStorage();\n    }\n    // 从本地存储加载数据\n    loadFromStorage() {\n        if (true) {\n            return {\n                users: [],\n                marketingCards: []\n            };\n        }\n        try {\n            const stored = localStorage.getItem(DB_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // 确保日期字段正确解析\n                parsed.users = parsed.users.map((user)=>({\n                        ...user,\n                        createdAt: new Date(user.createdAt)\n                    }));\n                // 确保营销卡片字段存在\n                if (!parsed.marketingCards) {\n                    parsed.marketingCards = [];\n                } else {\n                    parsed.marketingCards = parsed.marketingCards.map((card)=>({\n                            ...card,\n                            createdAt: new Date(card.createdAt),\n                            updatedAt: new Date(card.updatedAt)\n                        }));\n                }\n                return parsed;\n            }\n        } catch (error) {\n            console.error('加载数据失败:', error);\n        }\n        return {\n            users: [],\n            marketingCards: []\n        };\n    }\n    // 保存数据到本地存储\n    saveToStorage() {\n        if (true) return;\n        try {\n            localStorage.setItem(DB_KEY, JSON.stringify(this.data));\n        } catch (error) {\n            console.error('保存数据失败:', error);\n        }\n    }\n    // 创建虚拟用户（使用随机用户名）\n    async createUsers(domain, count) {\n        const newUsers = [];\n        const maxAttempts = count * 3; // 最多尝试次数，避免无限循环\n        let attempts = 0;\n        while(newUsers.length < count && attempts < maxAttempts){\n            attempts++;\n            // 生成随机用户名\n            const username = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateRandomUsername)();\n            const email = `${username}@${domain}`;\n            // 检查邮箱是否已存在\n            const existingUser = this.data.users.find((user)=>user.email === email);\n            if (existingUser) {\n                continue; // 跳过已存在的邮箱，重新生成\n            }\n            const user = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                email,\n                password: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateRandomPassword)(8),\n                createdAt: new Date(),\n                isActive: false\n            };\n            newUsers.push(user);\n            this.data.users.push(user);\n        }\n        this.saveToStorage();\n        return newUsers;\n    }\n    // 获取所有用户\n    async getAllUsers() {\n        return [\n            ...this.data.users\n        ];\n    }\n    // 根据邮箱获取用户\n    async getUserByEmail(email) {\n        return this.data.users.find((user)=>user.email === email) || null;\n    }\n    // 根据ID获取用户\n    async getUserById(id) {\n        return this.data.users.find((user)=>user.id === id) || null;\n    }\n    // 验证用户登录\n    async validateUser(email, password) {\n        const user = await this.getUserByEmail(email);\n        if (!user || !user.isActive) {\n            return null;\n        }\n        if (user.password === password) {\n            return user;\n        }\n        return null;\n    }\n    // 删除用户\n    async deleteUser(id) {\n        const index = this.data.users.findIndex((user)=>user.id === id);\n        if (index === -1) {\n            return false;\n        }\n        this.data.users.splice(index, 1);\n        this.saveToStorage();\n        return true;\n    }\n    // 切换用户状态\n    async toggleUserStatus(id) {\n        const user = this.data.users.find((user)=>user.id === id);\n        if (!user) {\n            return null;\n        }\n        user.isActive = !user.isActive;\n        // 如果是激活操作，记录激活时间\n        if (user.isActive) {\n            user.activatedAt = new Date();\n        }\n        this.saveToStorage();\n        return user;\n    }\n    // 更新用户信息\n    async updateUser(id, updates) {\n        const user = this.data.users.find((user)=>user.id === id);\n        if (!user) {\n            return null;\n        }\n        Object.assign(user, updates);\n        this.saveToStorage();\n        return user;\n    }\n    // 获取统计信息\n    async getStats() {\n        const totalUsers = this.data.users.length;\n        const activeUsers = this.data.users.filter((user)=>user.isActive).length;\n        const domains = [\n            ...new Set(this.data.users.map((user)=>user.email.split('@')[1]))\n        ];\n        return {\n            totalUsers,\n            activeUsers,\n            domains\n        };\n    }\n    // 清空所有数据\n    async clearAll() {\n        this.data = {\n            users: [],\n            marketingCards: []\n        };\n        this.saveToStorage();\n    }\n    // 导出数据\n    async exportData() {\n        return JSON.stringify(this.data, null, 2);\n    }\n    // 导入数据\n    async importData(jsonData) {\n        try {\n            const imported = JSON.parse(jsonData);\n            // 验证数据格式\n            if (!imported.users || !Array.isArray(imported.users)) {\n                throw new Error('无效的数据格式');\n            }\n            // 验证用户数据\n            for (const user of imported.users){\n                if (!user.id || !user.email || !user.password) {\n                    throw new Error('用户数据不完整');\n                }\n            }\n            this.data = {\n                users: imported.users.map((user)=>({\n                        ...user,\n                        createdAt: new Date(user.createdAt)\n                    })),\n                marketingCards: imported.marketingCards?.map((card)=>({\n                        ...card,\n                        createdAt: new Date(card.createdAt),\n                        updatedAt: new Date(card.updatedAt)\n                    })) || []\n            };\n            this.saveToStorage();\n            return true;\n        } catch (error) {\n            console.error('导入数据失败:', error);\n            return false;\n        }\n    }\n    // ==================== 营销卡片管理 ====================\n    // 获取所有营销卡片\n    async getAllMarketingCards() {\n        return [\n            ...this.data.marketingCards\n        ].sort((a, b)=>a.order - b.order);\n    }\n    // 获取激活的营销卡片\n    async getActiveMarketingCards() {\n        return this.data.marketingCards.filter((card)=>card.isActive).sort((a, b)=>a.order - b.order);\n    }\n    // 根据ID获取营销卡片\n    async getMarketingCardById(id) {\n        return this.data.marketingCards.find((card)=>card.id === id) || null;\n    }\n    // 创建营销卡片\n    async createMarketingCard(cardData) {\n        const now = new Date();\n        const newCard = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n            ...cardData,\n            createdAt: now,\n            updatedAt: now\n        };\n        this.data.marketingCards.push(newCard);\n        this.saveToStorage();\n        return newCard;\n    }\n    // 更新营销卡片\n    async updateMarketingCard(id, updates) {\n        const card = this.data.marketingCards.find((c)=>c.id === id);\n        if (!card) {\n            return null;\n        }\n        Object.assign(card, updates, {\n            updatedAt: new Date()\n        });\n        this.saveToStorage();\n        return card;\n    }\n    // 删除营销卡片\n    async deleteMarketingCard(id) {\n        const index = this.data.marketingCards.findIndex((card)=>card.id === id);\n        if (index === -1) {\n            return false;\n        }\n        this.data.marketingCards.splice(index, 1);\n        this.saveToStorage();\n        return true;\n    }\n    // 初始化默认营销卡片\n    async initializeDefaultMarketingCards() {\n        if (this.data.marketingCards.length === 0) {\n            const defaultCard = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                title: '🚀 LemooMail - 轻量级邮件管理系统',\n                description: '基于 Cloudflare Catch-All 转发的虚拟邮箱解决方案，零服务器成本，现代化技术栈，完美支持域名邮箱管理。无需复杂配置，一键部署即可使用。',\n                imageUrl: '',\n                buttonText: '立即开始使用',\n                buttonLink: '/login',\n                isActive: true,\n                order: 1,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            this.data.marketingCards.push(defaultCard);\n            this.saveToStorage();\n            console.log('默认营销卡片初始化完成');\n        }\n    }\n    // 初始化示例数据（仅初始化营销卡片，不创建测试用户）\n    async initSampleData() {\n        // 只初始化默认营销卡片，不创建测试用户\n        await this.initializeDefaultMarketingCards();\n        console.log('默认配置初始化完成');\n    }\n}\n// 创建数据库服务实例\nconst db = new DatabaseService();\n// 在客户端初始化示例数据\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/database.ts\n");

/***/ })

};
;