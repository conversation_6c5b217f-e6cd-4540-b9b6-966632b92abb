"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? {\n      type: anonymousActionType || findCallerName(new Error().stack) || \"anonymous\"\n    } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if (( false ? 0 : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vcmVhY3QubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDb0I7O0FBRTlDO0FBQ0E7QUFDQSxnQkFBZ0IsdURBQTBCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxnREFBbUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0REFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsiL1VzZXJzL3dhbmdqdW55YW8vZGV2ZWxvcC9hdWdtZW50LWVtYWlsL0xlbW9vTWFpbC9sZW1vby1tYWlsL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS9yZWFjdC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNyZWF0ZVN0b3JlIH0gZnJvbSAnenVzdGFuZC92YW5pbGxhJztcblxuY29uc3QgaWRlbnRpdHkgPSAoYXJnKSA9PiBhcmc7XG5mdW5jdGlvbiB1c2VTdG9yZShhcGksIHNlbGVjdG9yID0gaWRlbnRpdHkpIHtcbiAgY29uc3Qgc2xpY2UgPSBSZWFjdC51c2VTeW5jRXh0ZXJuYWxTdG9yZShcbiAgICBhcGkuc3Vic2NyaWJlLFxuICAgICgpID0+IHNlbGVjdG9yKGFwaS5nZXRTdGF0ZSgpKSxcbiAgICAoKSA9PiBzZWxlY3RvcihhcGkuZ2V0SW5pdGlhbFN0YXRlKCkpXG4gICk7XG4gIFJlYWN0LnVzZURlYnVnVmFsdWUoc2xpY2UpO1xuICByZXR1cm4gc2xpY2U7XG59XG5jb25zdCBjcmVhdGVJbXBsID0gKGNyZWF0ZVN0YXRlKSA9PiB7XG4gIGNvbnN0IGFwaSA9IGNyZWF0ZVN0b3JlKGNyZWF0ZVN0YXRlKTtcbiAgY29uc3QgdXNlQm91bmRTdG9yZSA9IChzZWxlY3RvcikgPT4gdXNlU3RvcmUoYXBpLCBzZWxlY3Rvcik7XG4gIE9iamVjdC5hc3NpZ24odXNlQm91bmRTdG9yZSwgYXBpKTtcbiAgcmV0dXJuIHVzZUJvdW5kU3RvcmU7XG59O1xuY29uc3QgY3JlYXRlID0gKGNyZWF0ZVN0YXRlKSA9PiBjcmVhdGVTdGF0ZSA/IGNyZWF0ZUltcGwoY3JlYXRlU3RhdGUpIDogY3JlYXRlSW1wbDtcblxuZXhwb3J0IHsgY3JlYXRlLCB1c2VTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vdmFuaWxsYS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEhBQThIO0FBQzlIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy93YW5nanVueWFvL2RldmVsb3AvYXVnbWVudC1lbWFpbC9MZW1vb01haWwvbGVtb28tbWFpbC9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vdmFuaWxsYS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY3JlYXRlU3RvcmVJbXBsID0gKGNyZWF0ZVN0YXRlKSA9PiB7XG4gIGxldCBzdGF0ZTtcbiAgY29uc3QgbGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgY29uc3Qgc2V0U3RhdGUgPSAocGFydGlhbCwgcmVwbGFjZSkgPT4ge1xuICAgIGNvbnN0IG5leHRTdGF0ZSA9IHR5cGVvZiBwYXJ0aWFsID09PSBcImZ1bmN0aW9uXCIgPyBwYXJ0aWFsKHN0YXRlKSA6IHBhcnRpYWw7XG4gICAgaWYgKCFPYmplY3QuaXMobmV4dFN0YXRlLCBzdGF0ZSkpIHtcbiAgICAgIGNvbnN0IHByZXZpb3VzU3RhdGUgPSBzdGF0ZTtcbiAgICAgIHN0YXRlID0gKHJlcGxhY2UgIT0gbnVsbCA/IHJlcGxhY2UgOiB0eXBlb2YgbmV4dFN0YXRlICE9PSBcIm9iamVjdFwiIHx8IG5leHRTdGF0ZSA9PT0gbnVsbCkgPyBuZXh0U3RhdGUgOiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwgbmV4dFN0YXRlKTtcbiAgICAgIGxpc3RlbmVycy5mb3JFYWNoKChsaXN0ZW5lcikgPT4gbGlzdGVuZXIoc3RhdGUsIHByZXZpb3VzU3RhdGUpKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IGdldFN0YXRlID0gKCkgPT4gc3RhdGU7XG4gIGNvbnN0IGdldEluaXRpYWxTdGF0ZSA9ICgpID0+IGluaXRpYWxTdGF0ZTtcbiAgY29uc3Qgc3Vic2NyaWJlID0gKGxpc3RlbmVyKSA9PiB7XG4gICAgbGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XG4gICAgcmV0dXJuICgpID0+IGxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xuICB9O1xuICBjb25zdCBhcGkgPSB7IHNldFN0YXRlLCBnZXRTdGF0ZSwgZ2V0SW5pdGlhbFN0YXRlLCBzdWJzY3JpYmUgfTtcbiAgY29uc3QgaW5pdGlhbFN0YXRlID0gc3RhdGUgPSBjcmVhdGVTdGF0ZShzZXRTdGF0ZSwgZ2V0U3RhdGUsIGFwaSk7XG4gIHJldHVybiBhcGk7XG59O1xuY29uc3QgY3JlYXRlU3RvcmUgPSAoY3JlYXRlU3RhdGUpID0+IGNyZWF0ZVN0YXRlID8gY3JlYXRlU3RvcmVJbXBsKGNyZWF0ZVN0YXRlKSA6IGNyZWF0ZVN0b3JlSW1wbDtcblxuZXhwb3J0IHsgY3JlYXRlU3RvcmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;