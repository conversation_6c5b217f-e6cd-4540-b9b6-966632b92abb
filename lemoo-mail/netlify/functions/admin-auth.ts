import { Handler } from '@netlify/functions';
import { createHmac } from 'crypto';

// 🔐 安全的管理员认证
const ADMIN_CREDENTIALS = {
  username: process.env.ADMIN_USERNAME || 'admin',
  password: process.env.ADMIN_PASSWORD || '', // 必须在环境变量中设置
  secretKey: process.env.ADMIN_SECRET_KEY || '', // JWT密钥
};

// 简单的速率限制（内存存储，重启后重置）
const loginAttempts = new Map<string, { count: number; lastAttempt: number }>();
const MAX_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60 * 1000; // 15分钟

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const attempts = loginAttempts.get(ip);

  if (!attempts) {
    return true; // 首次尝试
  }

  // 如果锁定时间已过，重置计数
  if (now - attempts.lastAttempt > LOCKOUT_TIME) {
    loginAttempts.delete(ip);
    return true;
  }

  // 检查是否超过最大尝试次数
  return attempts.count < MAX_ATTEMPTS;
}

function recordFailedAttempt(ip: string): void {
  const now = Date.now();
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };

  attempts.count += 1;
  attempts.lastAttempt = now;
  loginAttempts.set(ip, attempts);
}

function clearFailedAttempts(ip: string): void {
  loginAttempts.delete(ip);
}

// 🔐 安全的JWT实现
function createToken(payload: any): string {
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
  const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64url');

  // 使用HMAC-SHA256进行安全签名
  const signature = createHmac('sha256', ADMIN_CREDENTIALS.secretKey)
    .update(`${header}.${payloadStr}`)
    .digest('base64url');

  return `${header}.${payloadStr}.${signature}`;
}

function verifyToken(token: string): any {
  try {
    const [header, payload, signature] = token.split('.');

    // 使用HMAC-SHA256验证签名
    const expectedSignature = createHmac('sha256', ADMIN_CREDENTIALS.secretKey)
      .update(`${header}.${payload}`)
      .digest('base64url');

    if (signature !== expectedSignature) {
      return null;
    }

    const decodedPayload = JSON.parse(Buffer.from(payload, 'base64url').toString());

    // 检查过期时间
    if (decodedPayload.exp && Date.now() > decodedPayload.exp) {
      return null;
    }

    return decodedPayload;
  } catch {
    return null;
  }
}

export const handler: Handler = async (event, context) => {
  // 设置CORS头
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // 处理OPTIONS请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  try {
    if (event.httpMethod === 'POST') {
      // 管理员登录
      const { username, password } = JSON.parse(event.body || '{}');

      // 🔐 速率限制检查
      const clientIP = event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown';
      if (!checkRateLimit(clientIP)) {
        return {
          statusCode: 429,
          headers,
          body: JSON.stringify({
            success: false,
            message: '登录尝试过于频繁，请15分钟后再试',
          }),
        };
      }

      // 🔐 输入验证
      if (!username || !password) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            success: false,
            message: '用户名和密码不能为空',
          }),
        };
      }

      // 限制输入长度，防止DoS攻击
      if (username.length > 50 || password.length > 100) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            success: false,
            message: '输入长度超出限制',
          }),
        };
      }

      // 验证环境变量是否设置
      if (!ADMIN_CREDENTIALS.password || !ADMIN_CREDENTIALS.secretKey) {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            success: false,
            message: '服务器配置错误：管理员凭据未设置',
          }),
        };
      }
      
      // 验证凭据
      if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
        // 🔐 登录成功，清除失败记录
        clearFailedAttempts(clientIP);

        const token = createToken({
          username,
          role: 'admin',
          exp: Date.now() + 24 * 60 * 60 * 1000, // 24小时过期
        });

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            success: true,
            token,
            message: '管理员登录成功',
          }),
        };
      } else {
        // 🔐 登录失败，记录尝试
        recordFailedAttempt(clientIP);

        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误',
          }),
        };
      }
    }
    
    if (event.httpMethod === 'GET') {
      // 验证管理员token
      const authHeader = event.headers.authorization || event.headers.Authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({
            success: false,
            message: '缺少认证token',
          }),
        };
      }
      
      const token = authHeader.substring(7);
      const payload = verifyToken(token);
      
      if (!payload || payload.role !== 'admin') {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({
            success: false,
            message: 'token无效或已过期',
          }),
        };
      }
      
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          user: {
            username: payload.username,
            role: payload.role,
          },
        }),
      };
    }
    
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({
        success: false,
        message: '不支持的请求方法',
      }),
    };
    
  } catch (error) {
    console.error('管理员认证错误:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        message: '服务器内部错误',
      }),
    };
  }
};
