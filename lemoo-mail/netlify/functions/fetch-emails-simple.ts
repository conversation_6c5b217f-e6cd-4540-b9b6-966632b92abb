import { Handler } from '@netlify/functions';
import { ImapFlow } from 'imapflow';

// 🔐 HTML转义函数，防止XSS攻击
function escapeHtml(text: string): string {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

// 🔧 高级邮件内容清理函数
function cleanEmailContentAdvanced(content: string): string {
  console.log('开始智能清理，原始内容长度:', content.length);
  console.log('原始内容:', JSON.stringify(content));
  let cleaned = content;

  // 移除表格符号行
  cleaned = cleaned
    .split('\n')
    .filter(line => {
      const trimmed = line.trim();
      // 移除只包含表格符号的行
      if (/^[\|\s\-_=]+$/.test(trimmed)) {
        return false;
      }
      // 移除只包含单个字符重复的行（如 ||||| 或 -----）
      if (trimmed.length > 0 && /^(.)\1*$/.test(trimmed) && '|-_='.includes(trimmed[0])) {
        return false;
      }
      return true;
    })
    .join('\n');

  // 移除重复的邮箱地址和用户名信息
  const lines = cleaned.split('\n');
  const filteredLines: string[] = [];
  const seenContent = new Set<string>();

  for (const line of lines) {
    const trimmed = line.trim();

    // 跳过空行
    if (!trimmed) {
      // 保留空行，但避免连续多个空行
      if (filteredLines.length > 0 && filteredLines[filteredLines.length - 1].trim() !== '') {
        filteredLines.push('');
      }
      continue;
    }

    // 检查是否是重复的邮箱地址或用户名
    if (trimmed.includes('@') || (trimmed.length < 30 && !trimmed.includes(' '))) {
      // 如果这个内容已经在主要内容中出现过，跳过
      const mainContent = filteredLines.slice(0, Math.min(5, filteredLines.length)).join(' ').toLowerCase();
      if (mainContent.includes(trimmed.toLowerCase())) {
        console.log('跳过重复内容:', trimmed);
        continue;
      }
    }

    // 检查是否是纯邮箱地址或用户名（在邮件末尾的签名信息）
    if (filteredLines.length > 3) { // 如果已经有足够的主要内容
      if (trimmed.includes('@') ||
          (trimmed.length < 20 && /^[a-zA-Z0-9_.-]+$/.test(trimmed))) {
        console.log('跳过签名信息:', trimmed);
        continue;
      }
    }

    // 避免重复内容
    const normalizedLine = trimmed.toLowerCase();
    if (!seenContent.has(normalizedLine)) {
      seenContent.add(normalizedLine);
      filteredLines.push(line);
    }
  }

  // 清理结果
  cleaned = filteredLines.join('\n')
    .replace(/\n\s*\n\s*\n/g, '\n\n') // 合并多个空行
    .trim();

  console.log('智能清理完成，清理后内容长度:', cleaned.length);
  console.log('清理后内容:', JSON.stringify(cleaned));

  return cleaned;
}

// 🔧 专业邮件解析函数 - 使用mailparser库
async function parseEmailBodyProfessional(source: string): Promise<string> {
  try {
    console.log('开始专业邮件解析，源码长度:', source.length);

    // 动态导入mailparser
    const { simpleParser } = await import('mailparser');

    // 使用专业的邮件解析器
    const parsed = await simpleParser(source, {
      skipHtmlToText: false,  // 保留HTML到文本的转换
      skipTextToHtml: true,   // 跳过文本到HTML的转换
      skipImageLinks: true,   // 跳过图片链接处理
      skipTextLinks: false,   // 保留文本链接
      keepCidLinks: false,    // 不保留CID链接
    });

    console.log('邮件解析成功:', {
      subject: parsed.subject,
      from: parsed.from?.text,
      to: parsed.to?.text,
      hasText: !!parsed.text,
      hasHtml: !!parsed.html,
      textLength: parsed.text?.length || 0,
      htmlLength: parsed.html?.length || 0,
      attachments: parsed.attachments?.length || 0
    });

    // 优先使用纯文本内容，如果没有则从HTML提取
    let content = '';

    if (parsed.text && parsed.text.trim()) {
      content = parsed.text.trim();
      console.log('使用纯文本内容，长度:', content.length);
      console.log('原始纯文本内容:', JSON.stringify(content));
    } else if (parsed.html && parsed.html.trim()) {
      // 从HTML中提取纯文本
      content = parsed.html
        .replace(/<style[^>]*>.*?<\/style>/gis, '') // 移除样式
        .replace(/<script[^>]*>.*?<\/script>/gis, '') // 移除脚本
        .replace(/<[^>]*>/g, '') // 移除HTML标签
        .replace(/&nbsp;/g, ' ')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&#039;/g, "'")
        .replace(/&quot;/g, '"')
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim();
      console.log('从HTML提取文本内容，长度:', content.length);
    }

    if (!content) {
      console.log('未找到邮件正文内容');
      return '邮件内容为空';
    }

    // 智能清理和格式化内容
    content = content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 合并多个空行
      .trim();

    // 移除表格符号和重复信息
    content = cleanEmailContentAdvanced(content);

    // 如果内容过长，进行截断
    if (content.length > 2000) {
      content = content.substring(0, 2000) + '\n\n[内容已截断，显示前2000字符]';
    }

    console.log('最终解析内容长度:', content.length);
    console.log('最终解析内容预览:', content.substring(0, 200));

    return content;

  } catch (error) {
    console.error('专业邮件解析失败:', error);

    // 降级到简单解析
    console.log('降级到简单解析方法');
    return parseEmailBodySimple(source);
  }
}

// 🔧 简单邮件解析函数 - 降级方案
function parseEmailBodySimple(source: string): string {
  try {
    console.log('使用简单解析方法，源码长度:', source.length);

    // 查找Base64编码的内容
    const base64Matches = source.match(/Content-Transfer-Encoding:\s*base64\s*\n\s*\n([A-Za-z0-9+/=\s]+)/gi);

    if (base64Matches && base64Matches.length > 0) {
      console.log('找到Base64编码内容，数量:', base64Matches.length);

      for (const match of base64Matches) {
        try {
          const base64Content = match.replace(/Content-Transfer-Encoding:\s*base64\s*\n\s*\n/gi, '').trim();
          const decoded = Buffer.from(base64Content, 'base64').toString('utf-8');

          if (decoded && decoded.length > 10) {
            let cleanContent = decoded
              .replace(/<[^>]*>/g, '')
              .replace(/&nbsp;/g, ' ')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&#039;/g, "'")
              .replace(/&quot;/g, '"')
              .replace(/\r\n/g, '\n')
              .replace(/\r/g, '\n')
              .replace(/\n\s*\n\s*\n/g, '\n\n')
              .trim();

            if (cleanContent.length > 1000) {
              cleanContent = cleanContent.substring(0, 1000) + '\n\n[内容已截断...]';
            }

            return cleanContent;
          }
        } catch (decodeError) {
          console.error('Base64解码失败:', decodeError);
          continue;
        }
      }
    }

    // 查找纯文本内容
    const lines = source.split('\n');
    let bodyStartIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() === '') {
        bodyStartIndex = i + 1;
        break;
      }
    }

    if (bodyStartIndex !== -1) {
      const bodyContent = lines.slice(bodyStartIndex).join('\n').trim();
      if (bodyContent && bodyContent.length > 10) {
        return bodyContent.length > 1000 ?
          bodyContent.substring(0, 1000) + '\n\n[内容已截断...]' :
          bodyContent;
      }
    }

    return '无法解析邮件内容';

  } catch (error) {
    console.error('简单邮件解析失败:', error);
    return '邮件解析失败';
  }
}



// 🔐 安全的邮件配置
const IMAP_CONFIG = {
  host: process.env.IMAP_HOST || 'imap.163.com',
  port: parseInt(process.env.IMAP_PORT || '993'),
  secure: true,
  auth: {
    user: process.env.IMAP_USER || '',
    pass: process.env.IMAP_PASSWORD || '',
  }
};

// 邮件类型定义
interface Email {
  id: string;
  uid: number;
  to: string;
  from: string;
  subject: string;
  date: Date;
  body: string;
  isRead: boolean;
  isStarred: boolean;
  attachments: any[];
}

// 简化的邮件获取函数
async function fetchSimpleEmails(targetEmail: string): Promise<Email[]> {
  console.log(`开始获取 ${targetEmail} 的邮件`);
  
  const client = new ImapFlow(IMAP_CONFIG);
  
  try {
    console.log('正在连接IMAP服务器...');
    await client.connect();
    console.log('IMAP连接成功');
    
    const lock = await client.getMailboxLock('INBOX');
    
    try {
      // 获取邮箱状态
      const status = await client.status('INBOX', { messages: true });
      console.log(`收件箱共有 ${status.messages} 封邮件`);
      
      if (!status.messages || status.messages === 0) {
        return [];
      }
      
      // 获取最新10封邮件
      const count = Math.min(status.messages, 10);
      const start = Math.max(1, status.messages - count + 1);
      const range = `${start}:${status.messages}`;
      
      console.log(`获取邮件范围: ${range}`);
      
      const messages = client.fetch(range, {
        envelope: true,
        source: true,
        flags: true,
        uid: true
      });
      
      const emails: Email[] = [];
      
      for await (const message of messages) {
        console.log(`处理邮件 UID=${message.uid}, 主题=${message.envelope?.subject}`);
        
        // 简单匹配：检查邮件源码中是否包含目标邮箱
        const source = message.source?.toString() || '';
        const containsTarget = source.toLowerCase().includes(targetEmail.toLowerCase());
        
        if (containsTarget) {
          console.log(`邮件 ${message.uid} 匹配成功`);

          // 使用专业的邮件解析器
          const realBody = await parseEmailBodyProfessional(message.source?.toString() || '');

          const email: Email = {
            id: `real_${message.uid}`,
            uid: message.uid,
            to: targetEmail,
            from: message.envelope?.from?.[0]?.address || '未知发件人',
            subject: message.envelope?.subject || '无主题',
            date: message.envelope?.date || new Date(),
            body: realBody,
            isRead: !message.flags?.has('\\Unseen'),
            isStarred: message.flags?.has('\\Flagged') || false,
            attachments: [],
          };

          emails.push(email);
        }
      }
      
      console.log(`找到 ${emails.length} 封匹配的邮件`);
      return emails.sort((a, b) => b.date.getTime() - a.date.getTime());
      
    } finally {
      lock.release();
    }
    
  } catch (error) {
    console.error('IMAP操作失败:', error);
    throw error;
  } finally {
    try {
      await client.logout();
    } catch (e) {
      console.warn('关闭连接失败:', e);
    }
  }
}

export const handler: Handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, message: '只支持GET请求' }),
    };
  }

  try {
    const userEmail = event.queryStringParameters?.email;
    
    if (!userEmail) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ success: false, message: '缺少用户邮箱参数' }),
      };
    }

    const cleanEmail = escapeHtml(userEmail.trim());

    if (!IMAP_CONFIG.auth.user || !IMAP_CONFIG.auth.pass) {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          emails: [],
          total: 0,
          message: 'IMAP未配置，无法获取真实邮件',
        }),
      };
    }

    const emails = await fetchSimpleEmails(cleanEmail);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        emails: emails,
        total: emails.length,
        message: `成功获取 ${emails.length} 封邮件`,
      }),
    };

  } catch (error) {
    console.error('获取邮件失败:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        message: '服务器内部错误',
      }),
    };
  }
};
