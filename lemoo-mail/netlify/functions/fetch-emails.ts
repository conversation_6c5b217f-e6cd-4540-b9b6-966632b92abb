import { Handler } from '@netlify/functions';
import { ImapFlow } from 'imapflow';

// 🔐 HTML转义函数，防止XSS攻击
function escapeHtml(text: string): string {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

// 🔧 高级邮件内容清理函数
function cleanEmailContentAdvanced(content: string): string {
  console.log('开始智能清理，原始内容长度:', content.length);
  console.log('原始内容:', JSON.stringify(content));
  let cleaned = content;

  // 移除表格符号行
  cleaned = cleaned
    .split('\n')
    .filter(line => {
      const trimmed = line.trim();
      // 移除只包含表格符号的行
      if (/^[\|\s\-_=]+$/.test(trimmed)) {
        return false;
      }
      // 移除只包含单个字符重复的行（如 ||||| 或 -----）
      if (trimmed.length > 0 && /^(.)\1*$/.test(trimmed) && '|-_='.includes(trimmed[0])) {
        return false;
      }
      return true;
    })
    .join('\n');

  // 移除重复的邮箱地址和用户名信息
  const lines = cleaned.split('\n');
  const filteredLines: string[] = [];
  const seenContent = new Set<string>();

  for (const line of lines) {
    const trimmed = line.trim();

    // 跳过空行
    if (!trimmed) {
      // 保留空行，但避免连续多个空行
      if (filteredLines.length > 0 && filteredLines[filteredLines.length - 1].trim() !== '') {
        filteredLines.push('');
      }
      continue;
    }

    // 检查是否是重复的邮箱地址或用户名
    if (trimmed.includes('@') || (trimmed.length < 30 && !trimmed.includes(' '))) {
      // 如果这个内容已经在主要内容中出现过，跳过
      const mainContent = filteredLines.slice(0, Math.min(5, filteredLines.length)).join(' ').toLowerCase();
      if (mainContent.includes(trimmed.toLowerCase())) {
        console.log('跳过重复内容:', trimmed);
        continue;
      }
    }

    // 检查是否是纯邮箱地址或用户名（在邮件末尾的签名信息）
    if (filteredLines.length > 3) { // 如果已经有足够的主要内容
      if (trimmed.includes('@') ||
          (trimmed.length < 20 && /^[a-zA-Z0-9_.-]+$/.test(trimmed))) {
        console.log('跳过签名信息:', trimmed);
        continue;
      }
    }

    // 避免重复内容
    const normalizedLine = trimmed.toLowerCase();
    if (!seenContent.has(normalizedLine)) {
      seenContent.add(normalizedLine);
      filteredLines.push(line);
    }
  }

  // 清理结果
  cleaned = filteredLines.join('\n')
    .replace(/\n\s*\n\s*\n/g, '\n\n') // 合并多个空行
    .trim();

  console.log('智能清理完成，清理后内容长度:', cleaned.length);
  console.log('清理后内容:', JSON.stringify(cleaned));

  return cleaned;
}

// 🔧 专业邮件解析函数 - 使用mailparser库
async function parseEmailBodyProfessional(source: string): Promise<string> {
  try {
    console.log('开始专业邮件解析，源码长度:', source.length);

    // 动态导入mailparser
    const { simpleParser } = await import('mailparser');

    // 使用专业的邮件解析器
    const parsed = await simpleParser(source, {
      skipHtmlToText: false,  // 保留HTML到文本的转换
      skipTextToHtml: true,   // 跳过文本到HTML的转换
      skipImageLinks: true,   // 跳过图片链接处理
      skipTextLinks: false,   // 保留文本链接
      keepCidLinks: false,    // 不保留CID链接
    });

    console.log('邮件解析成功:', {
      subject: parsed.subject,
      from: parsed.from?.text,
      to: parsed.to?.text,
      hasText: !!parsed.text,
      hasHtml: !!parsed.html,
      textLength: parsed.text?.length || 0,
      htmlLength: parsed.html?.length || 0,
      attachments: parsed.attachments?.length || 0
    });

    // 优先使用纯文本内容，如果没有则从HTML提取
    let content = '';

    if (parsed.text && parsed.text.trim()) {
      content = parsed.text.trim();
      console.log('使用纯文本内容，长度:', content.length);
      console.log('原始纯文本内容:', JSON.stringify(content));
    } else if (parsed.html && parsed.html.trim()) {
      // 从HTML中提取纯文本
      content = parsed.html
        .replace(/<style[^>]*>.*?<\/style>/gis, '') // 移除样式
        .replace(/<script[^>]*>.*?<\/script>/gis, '') // 移除脚本
        .replace(/<[^>]*>/g, '') // 移除HTML标签
        .replace(/&nbsp;/g, ' ')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&#039;/g, "'")
        .replace(/&quot;/g, '"')
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim();
      console.log('从HTML提取文本内容，长度:', content.length);
    }

    if (!content) {
      console.log('未找到邮件正文内容');
      return '邮件内容为空';
    }

    // 智能清理和格式化内容
    content = content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 合并多个空行
      .trim();

    // 移除表格符号和重复信息
    content = cleanEmailContentAdvanced(content);

    // 如果内容过长，进行截断
    if (content.length > 2000) {
      content = content.substring(0, 2000) + '\n\n[内容已截断，显示前2000字符]';
    }

    console.log('最终解析内容长度:', content.length);
    console.log('最终解析内容预览:', content.substring(0, 200));

    return content;

  } catch (error) {
    console.error('专业邮件解析失败:', error);

    // 降级到简单解析
    console.log('降级到简单解析方法');
    return parseEmailBodySimple(source);
  }
}

// 🔧 简单邮件解析函数 - 降级方案
function parseEmailBodySimple(source: string): string {
  try {
    console.log('使用简单解析方法，源码长度:', source.length);

    // 查找Base64编码的内容
    const base64Matches = source.match(/Content-Transfer-Encoding:\s*base64\s*\n\s*\n([A-Za-z0-9+/=\s]+)/gi);

    if (base64Matches && base64Matches.length > 0) {
      console.log('找到Base64编码内容，数量:', base64Matches.length);

      for (const match of base64Matches) {
        try {
          const base64Content = match.replace(/Content-Transfer-Encoding:\s*base64\s*\n\s*\n/gi, '').trim();
          const decoded = Buffer.from(base64Content, 'base64').toString('utf-8');

          if (decoded && decoded.length > 10) {
            let cleanContent = decoded
              .replace(/<[^>]*>/g, '')
              .replace(/&nbsp;/g, ' ')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&#039;/g, "'")
              .replace(/&quot;/g, '"')
              .replace(/\r\n/g, '\n')
              .replace(/\r/g, '\n')
              .replace(/\n\s*\n\s*\n/g, '\n\n')
              .trim();

            if (cleanContent.length > 1000) {
              cleanContent = cleanContent.substring(0, 1000) + '\n\n[内容已截断...]';
            }

            return cleanContent;
          }
        } catch (decodeError) {
          console.error('Base64解码失败:', decodeError);
          continue;
        }
      }
    }

    // 查找纯文本内容
    const lines = source.split('\n');
    let bodyStartIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() === '') {
        bodyStartIndex = i + 1;
        break;
      }
    }

    if (bodyStartIndex !== -1) {
      const bodyContent = lines.slice(bodyStartIndex).join('\n').trim();
      if (bodyContent && bodyContent.length > 10) {
        return bodyContent.length > 1000 ?
          bodyContent.substring(0, 1000) + '\n\n[内容已截断...]' :
          bodyContent;
      }
    }

    return '无法解析邮件内容';

  } catch (error) {
    console.error('简单邮件解析失败:', error);
    return '邮件解析失败';
  }
}



// 🔐 安全的邮件配置 - 只在服务端使用环境变量
const IMAP_CONFIG = {
  host: process.env.IMAP_HOST || 'imap.163.com',
  port: parseInt(process.env.IMAP_PORT || '993'),
  secure: true,
  auth: {
    user: process.env.IMAP_USER || '', // 必须在环境变量中设置
    pass: process.env.IMAP_PASSWORD || '', // 必须在环境变量中设置
  }
};

// 邮件类型定义
interface Email {
  id: string;
  uid: number;
  to: string;
  from: string;
  subject: string;
  date: Date;
  body: string;
  isRead: boolean;
  isStarred: boolean;
  attachments: any[];
}

// 解析原始收件人地址（从邮件头部）
function parseOriginalRecipient(emailSource: string, envelope: any): string | null {
  const lines = emailSource.split('\n');

  // 查找各种可能的收件人头部
  const headers = [
    'x-original-to:',
    'delivered-to:',
    'x-envelope-to:',
    'x-rcpt-to:',
    'to:'
  ];

  for (const header of headers) {
    for (const line of lines) {
      if (line.toLowerCase().startsWith(header)) {
        const recipient = line.substring(header.length).trim();
        if (recipient) {
          console.log(`找到收件人头部 ${header} ${recipient}`);
          return recipient;
        }
      }
    }
  }

  // 如果没有找到特殊头部，使用envelope中的to字段
  if (envelope?.to && envelope.to.length > 0) {
    const recipient = envelope.to[0].address;
    console.log(`使用envelope收件人: ${recipient}`);
    return recipient;
  }

  return null;
}

// 从IMAP获取真实邮件
async function fetchRealEmails(targetEmail: string): Promise<Email[]> {
  const client = new ImapFlow(IMAP_CONFIG);

  try {
    // 连接到IMAP服务器
    await client.connect();
    console.log('IMAP连接成功');

    // 选择收件箱
    const lock = await client.getMailboxLock('INBOX');

    try {
      // 获取最近30天的邮件
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // 搜索最近的邮件（获取最新100封）
      const messages = client.fetch('1:100', {
        envelope: true,
        bodyStructure: true,
        source: true,
        flags: true,
        uid: true
      });

      const emails: Email[] = [];

      let processedCount = 0;
      let matchedCount = 0;

      for await (const message of messages) {
        try {
          processedCount++;
          console.log(`处理邮件 ${processedCount}: UID=${message.uid}`);

          // 解析邮件源码获取原始收件人
          const source = message.source?.toString() || '';
          const originalRecipient = parseOriginalRecipient(source, message.envelope);

          console.log(`邮件 ${message.uid} 收件人: ${originalRecipient}, 目标: ${targetEmail}`);

          // 检查是否匹配目标邮箱（支持多种匹配方式）
          const isMatch = originalRecipient && (
            originalRecipient.toLowerCase() === targetEmail.toLowerCase() ||
            originalRecipient.toLowerCase().includes(targetEmail.toLowerCase()) ||
            targetEmail.toLowerCase().includes(originalRecipient.toLowerCase())
          );

          if (isMatch) {
            matchedCount++;
            console.log(`邮件 ${message.uid} 匹配成功`);
            // 获取邮件正文
            let body = '';
            try {
              const bodyPart = await client.download(message.uid, '1', {
                maxBytes: 10000 // 限制邮件大小
              });
              body = bodyPart.toString();
            } catch (bodyError) {
              console.warn('获取邮件正文失败:', bodyError);
              body = '邮件正文获取失败';
            }

            const email: Email = {
              id: `real_${message.uid}`,
              uid: message.uid,
              to: originalRecipient,
              from: message.envelope?.from?.[0]?.address || '未知发件人',
              subject: message.envelope?.subject || '无主题',
              date: message.envelope?.date || new Date(),
              body: body,
              isRead: !message.flags?.has('\\Unseen'),
              isStarred: message.flags?.has('\\Flagged') || false,
              attachments: [],
            };

            emails.push(email);
          } else {
            console.log(`邮件 ${message.uid} 不匹配，跳过`);
          }
        } catch (messageError) {
          console.warn('处理邮件失败:', messageError);
        }
      }

      console.log(`处理完成: 总共 ${processedCount} 封邮件, 匹配 ${matchedCount} 封, 最终返回 ${emails.length} 封`);

      // 按日期排序（最新的在前）
      return emails.sort((a, b) => b.date.getTime() - a.date.getTime());

    } finally {
      lock.release();
    }

  } catch (error) {
    console.error('IMAP连接失败:', error);
    throw error;
  } finally {
    await client.logout();
  }
}

// 生成模拟邮件数据（临时方案，直到IMAP连接实现）
function generateMockEmails(userEmail: string): Email[] {
  const senders = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  const subjects = [
    '欢迎使用我们的服务',
    '重要通知：账户安全更新',
    '您的订单已确认',
    '月度报告已生成',
    '系统维护通知',
    '新功能发布公告'
  ];

  const emails: Email[] = [];
  
  // 为用户生成3-6封邮件
  const emailCount = Math.floor(Math.random() * 4) + 3;
  
  for (let i = 0; i < emailCount; i++) {
    const sender = senders[Math.floor(Math.random() * senders.length)];
    const subject = subjects[Math.floor(Math.random() * subjects.length)];
    const daysAgo = Math.floor(Math.random() * 30);
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    
    emails.push({
      id: `email_${Date.now()}_${i}`,
      uid: i + 1,
      to: userEmail,
      from: sender,
      subject: subject,
      date: date,
      body: `这是一封发送给 ${userEmail} 的测试邮件。\n\n主题：${subject}\n发送时间：${date.toLocaleString('zh-CN')}\n\n这是邮件正文的示例内容。`,
      isRead: Math.random() > 0.3,
      isStarred: Math.random() > 0.8,
      attachments: [],
    });
  }

  return emails.sort((a, b) => b.date.getTime() - a.date.getTime());
}

export const handler: Handler = async (event, context) => {
  // 设置CORS头
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // 处理OPTIONS请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({
        success: false,
        message: '只支持GET请求',
      }),
    };
  }

  try {
    // 从查询参数获取用户邮箱
    const userEmail = event.queryStringParameters?.email;
    
    if (!userEmail) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          message: '缺少用户邮箱参数',
        }),
      };
    }

    // 🔐 输入验证和清理
    const cleanEmail = escapeHtml(userEmail.trim());

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleanEmail)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          message: '邮箱格式无效',
        }),
      };
    }

    // 限制邮箱长度
    if (cleanEmail.length > 100) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          message: '邮箱地址过长',
        }),
      };
    }

    // 检查环境变量配置
    if (!IMAP_CONFIG.auth.user || !IMAP_CONFIG.auth.pass) {
      console.warn('IMAP配置未设置，使用模拟数据');

      // 返回模拟数据
      const mockEmails = generateMockEmails(cleanEmail);

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          emails: mockEmails,
          total: mockEmails.length,
          message: '使用模拟数据（IMAP未配置）',
        }),
      };
    }

    // 尝试获取真实邮件
    try {
      console.log(`尝试获取 ${cleanEmail} 的真实邮件`);

      // 使用简化的邮件获取逻辑
      const client = new ImapFlow(IMAP_CONFIG);

      try {
        await client.connect();
        console.log('IMAP连接成功');

        const lock = await client.getMailboxLock('INBOX');

        try {
          const status = await client.status('INBOX', { messages: true });
          console.log(`收件箱共有 ${status.messages} 封邮件`);

          if (!status.messages || status.messages === 0) {
            return {
              statusCode: 200,
              headers,
              body: JSON.stringify({
                success: true,
                emails: [],
                total: 0,
                message: '收件箱为空',
              }),
            };
          }

          // 获取最新20封邮件
          const count = Math.min(status.messages, 20);
          const start = Math.max(1, status.messages - count + 1);
          const range = `${start}:${status.messages}`;

          const messages = client.fetch(range, {
            envelope: true,
            source: true,
            flags: true,
            uid: true
          });

          const emails: Email[] = [];

          for await (const message of messages) {
            const source = message.source?.toString() || '';
            const containsTarget = source.toLowerCase().includes(cleanEmail.toLowerCase());

            if (containsTarget) {
              // 使用专业的邮件解析器
              const realBody = await parseEmailBodyProfessional(source);

              const email: Email = {
                id: `real_${message.uid}`,
                uid: message.uid,
                to: cleanEmail,
                from: message.envelope?.from?.[0]?.address || '未知发件人',
                subject: message.envelope?.subject || '无主题',
                date: message.envelope?.date || new Date(),
                body: realBody,
                isRead: !message.flags?.has('\\Unseen'),
                isStarred: message.flags?.has('\\Flagged') || false,
                attachments: [],
              };

              emails.push(email);
            }
          }

          const sortedEmails = emails.sort((a, b) => b.date.getTime() - a.date.getTime());

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
              success: true,
              emails: sortedEmails,
              total: sortedEmails.length,
              message: `成功获取 ${sortedEmails.length} 封真实邮件`,
            }),
          };

        } finally {
          lock.release();
        }

      } finally {
        try {
          await client.logout();
        } catch (e) {
          console.warn('关闭IMAP连接失败:', e);
        }
      }

    } catch (imapError) {
      console.error('IMAP获取邮件失败，回退到模拟数据:', imapError);

      // 如果IMAP失败，回退到模拟数据
      const mockEmails = generateMockEmails(cleanEmail);

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          emails: mockEmails,
          total: mockEmails.length,
          message: '邮件服务暂时不可用，显示模拟数据',
        }),
      };
    }

  } catch (error) {
    console.error('获取邮件失败:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        message: '服务器内部错误',
      }),
    };
  }
};
