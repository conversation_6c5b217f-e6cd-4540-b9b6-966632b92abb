#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔐 LemooMail 安全检查工具');
console.log('==========================');
console.log('');

// 检查环境变量配置
function checkEnvConfig() {
  console.log('📋 检查环境变量配置...');
  
  const requiredEnvVars = [
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD', 
    'ADMIN_SECRET_KEY',
    'IMAP_USER',
    'IMAP_PASSWORD'
  ];
  
  const missing = [];
  const weak = [];
  
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    
    if (!value) {
      missing.push(envVar);
      continue;
    }
    
    // 检查密码强度
    if (envVar === 'ADMIN_PASSWORD') {
      if (value.length < 12) {
        weak.push(`${envVar}: 密码长度不足12位`);
      }
      if (!/[a-z]/.test(value)) {
        weak.push(`${envVar}: 缺少小写字母`);
      }
      if (!/[A-Z]/.test(value)) {
        weak.push(`${envVar}: 缺少大写字母`);
      }
      if (!/[0-9]/.test(value)) {
        weak.push(`${envVar}: 缺少数字`);
      }
      if (!/[^a-zA-Z0-9]/.test(value)) {
        weak.push(`${envVar}: 缺少特殊字符`);
      }
    }
    
    // 检查JWT密钥长度
    if (envVar === 'ADMIN_SECRET_KEY' && value.length < 32) {
      weak.push(`${envVar}: JWT密钥长度不足32位`);
    }
  }
  
  if (missing.length > 0) {
    console.log('❌ 缺少必需的环境变量:');
    missing.forEach(env => console.log(`   - ${env}`));
    console.log('');
  }
  
  if (weak.length > 0) {
    console.log('⚠️  密码强度不足:');
    weak.forEach(issue => console.log(`   - ${issue}`));
    console.log('');
  }
  
  if (missing.length === 0 && weak.length === 0) {
    console.log('✅ 环境变量配置正确');
  }
  
  return missing.length === 0 && weak.length === 0;
}

// 检查代码中的安全问题
function checkCodeSecurity() {
  console.log('🔍 检查代码安全性...');
  
  const issues = [];
  
  // 检查是否有硬编码的敏感信息
  const filesToCheck = [
    'src/lib/mail-service.ts',
    'src/lib/database.ts',
    'src/store/auth.ts',
    'src/store/admin.ts',
    'src/store/mail.ts'
  ];
  
  for (const file of filesToCheck) {
    const filePath = path.join(process.cwd(), file);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查硬编码的邮箱
    if (content.includes('@163.com') && !content.includes('process.env')) {
      issues.push(`${file}: 发现硬编码的163邮箱地址`);
    }
    
    // 检查硬编码的密码
    if (content.match(/password.*=.*['"][^'"]{8,}['"]/i)) {
      issues.push(`${file}: 可能包含硬编码的密码`);
    }
    
    // 检查不安全的eval使用
    if (content.includes('eval(')) {
      issues.push(`${file}: 使用了不安全的eval函数`);
    }
    
    // 检查innerHTML使用
    if (content.includes('innerHTML')) {
      issues.push(`${file}: 使用innerHTML可能导致XSS攻击`);
    }
  }
  
  if (issues.length > 0) {
    console.log('❌ 发现安全问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
    console.log('');
  } else {
    console.log('✅ 代码安全检查通过');
  }
  
  return issues.length === 0;
}

// 检查文件权限
function checkFilePermissions() {
  console.log('📁 检查文件权限...');
  
  const sensitiveFiles = [
    '.env',
    '.env.local',
    '.env.production'
  ];
  
  let hasIssues = false;
  
  for (const file of sensitiveFiles) {
    const filePath = path.join(process.cwd(), file);
    
    if (fs.existsSync(filePath)) {
      try {
        const stats = fs.statSync(filePath);
        const mode = stats.mode & parseInt('777', 8);
        
        if (mode > parseInt('600', 8)) {
          console.log(`⚠️  ${file} 权限过于宽松 (${mode.toString(8)})`);
          hasIssues = true;
        }
      } catch (error) {
        console.log(`❌ 无法检查 ${file} 的权限`);
        hasIssues = true;
      }
    }
  }
  
  if (!hasIssues) {
    console.log('✅ 文件权限检查通过');
  }
  
  return !hasIssues;
}

// 主检查函数
function runSecurityCheck() {
  const envCheck = checkEnvConfig();
  console.log('');
  
  const codeCheck = checkCodeSecurity();
  console.log('');
  
  const fileCheck = checkFilePermissions();
  console.log('');
  
  console.log('📊 安全检查总结:');
  console.log('================');
  
  if (envCheck && codeCheck && fileCheck) {
    console.log('🎉 所有安全检查都通过了！');
    console.log('');
    console.log('✅ 环境变量配置正确');
    console.log('✅ 代码安全性良好');
    console.log('✅ 文件权限合适');
    console.log('');
    console.log('🚀 系统可以安全部署！');
    process.exit(0);
  } else {
    console.log('❌ 发现安全问题，请修复后再部署');
    console.log('');
    console.log('📖 请参考以下文档:');
    console.log('   - SECURITY.md: 安全指南');
    console.log('   - DEPLOYMENT.md: 部署指南');
    console.log('   - .env.example: 环境变量示例');
    process.exit(1);
  }
}

// 运行检查
runSecurityCheck();
