#!/usr/bin/env node

const crypto = require('crypto');

// 生成强密码
function generateStrongPassword(length = 16) {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  let password = '';
  
  // 确保至少包含每种类型的字符
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // 填充剩余长度
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // 打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

// 生成JWT密钥
function generateJWTSecret() {
  return crypto.randomBytes(32).toString('hex');
}

console.log('🔐 LemooMail 安全配置生成器');
console.log('================================');
console.log('');
console.log('请将以下配置添加到 Netlify 环境变量中：');
console.log('');
console.log('ADMIN_USERNAME=admin');
console.log(`ADMIN_PASSWORD=${generateStrongPassword(16)}`);
console.log(`ADMIN_SECRET_KEY=${generateJWTSecret()}`);
console.log('');
console.log('⚠️  重要提醒：');
console.log('1. 请妥善保存这些密码，丢失后需要重新生成');
console.log('2. 不要将这些信息提交到Git仓库');
console.log('3. 定期更换密码以提高安全性');
console.log('4. IMAP_PASSWORD 需要单独在163邮箱中获取授权码');
