// 虚拟用户类型定义
export interface VirtualUser {
  id: string;
  email: string;        // <EMAIL>
  password: string;     // 8位随机密码(仅用于登录系统)
  createdAt: Date;
  isActive: boolean;
  activatedAt?: Date;   // 激活时间
}

// 邮件类型定义
export interface Email {
  id: string;
  uid: number;
  to: string;           // 原始收件人 <EMAIL>
  from: string;         // 发件人
  subject: string;      // 邮件主题
  date: Date;          // 邮件日期
  body: string;        // 邮件内容
  isRead: boolean;     // 是否已读
  isStarred: boolean;  // 是否标星
  attachments?: EmailAttachment[];
}

// 邮件附件类型
export interface EmailAttachment {
  id: string;
  filename: string;
  size: number;
  contentType: string;
  url?: string;
}

// 邮件文件夹类型
export interface EmailFolder {
  id: string;
  name: string;
  count: number;
  icon?: string;
}

// 管理员创建用户请求类型
export interface CreateUsersRequest {
  domain: string;
  count: number;
  prefix?: string;  // 用户名前缀，默认为 "user"
}

// 管理员创建用户响应类型
export interface CreateUsersResponse {
  success: boolean;
  users: VirtualUser[];
  message?: string;
}

// 用户登录请求类型
export interface LoginRequest {
  email: string;
  password: string;
}

// 用户登录响应类型
export interface LoginResponse {
  success: boolean;
  user?: VirtualUser;
  token?: string;
  message?: string;
}

// 邮件获取响应类型
export interface EmailsResponse {
  success: boolean;
  emails: Email[];
  total: number;
  message?: string;
}

// 系统配置类型
export interface SystemConfig {
  imapHost: string;
  imapPort: number;
  imapUser: string;
  imapPassword: string;
  defaultDomain: string;
  maxUsersPerDomain: number;
}

// 营销卡片类型
export interface MarketingCard {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  buttonText: string;
  buttonLink: string;
  isActive: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

// API响应基础类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 邮件筛选参数类型
export interface EmailFilters {
  folder?: string;
  isRead?: boolean;
  isStarred?: boolean;
  from?: string;
  subject?: string;
  dateFrom?: Date;
  dateTo?: Date;
}
