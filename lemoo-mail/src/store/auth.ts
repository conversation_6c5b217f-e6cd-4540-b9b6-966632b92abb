import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { VirtualUser } from '@/types';

interface AuthState {
  user: VirtualUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  setUser: (user: VirtualUser) => void;
  setToken: (token: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录操作
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          // 直接使用数据库服务验证
          const { db } = await import('@/lib/database');
          const user = await db.validateUser(email, password);

          if (user) {
            // 生成简单的token
            const token = Buffer.from(`${user.id}:${Date.now()}`).toString('base64');

            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({
              error: '邮箱或密码错误，或账户已被禁用',
              isLoading: false,
            });
            return false;
          }
        } catch (error) {
          set({
            error: '登录验证失败，请稍后重试',
            isLoading: false,
          });
          return false;
        }
      },

      // 登出操作
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
      },

      // 设置用户信息
      setUser: (user: VirtualUser) => {
        set({ user, isAuthenticated: true });
      },

      // 设置token
      setToken: (token: string) => {
        set({ token });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      },

      // 清除错误信息
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
