import { create } from 'zustand';
import type { Em<PERSON>, Em<PERSON><PERSON>older, EmailFilters } from '@/types';

interface MailState {
  emails: Email[];
  selectedEmail: Email | null;
  currentFolder: string;
  folders: EmailFolder[];
  filters: EmailFilters;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  unreadCount: number;
  lastFetchTime: number | null;
  autoRefreshEnabled: boolean;
  refreshInterval: number; // 秒
}

interface MailActions {
  fetchEmails: (userEmail: string) => Promise<void>;
  selectEmail: (email: Email) => void;
  markAsRead: (emailId: string) => void;
  markAsUnread: (emailId: string) => void;
  toggleStar: (emailId: string) => void;
  deleteEmail: (emailId: string) => void;
  setCurrentFolder: (folder: string) => void;
  setFilters: (filters: Partial<EmailFilters>) => void;
  setSearchQuery: (query: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  refreshEmails: (userEmail: string) => Promise<void>;
  setAutoRefresh: (enabled: boolean) => void;
  setRefreshInterval: (seconds: number) => void;
  shouldRefresh: () => boolean;
}

type MailStore = MailState & MailActions;

export const useMailStore = create<MailStore>((set, get) => ({
  // 初始状态
  emails: [],
  selectedEmail: null,
  currentFolder: 'inbox',
  folders: [
    { id: 'inbox', name: '收件箱', count: 0, icon: 'inbox' },
  ],
  filters: {},
  isLoading: false,
  error: null,
  searchQuery: '',
  unreadCount: 0,
  lastFetchTime: null,
  autoRefreshEnabled: false, // 默认关闭自动刷新
  refreshInterval: 30, // 30秒间隔

  // 获取邮件列表
  fetchEmails: async (userEmail: string) => {
    console.log('📧 开始获取邮件 for:', userEmail);
    set({ isLoading: true, error: null });

    try {
      // 🔐 使用安全的Netlify Functions获取邮件
      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:8888'
        : '';

      const url = `${baseUrl}/.netlify/functions/fetch-emails?email=${encodeURIComponent(userEmail)}`;
      console.log('📧 请求URL:', url);

      const response = await fetch(url);
      const data = await response.json();

      console.log('📧 API响应:', data);

      if (data.success) {
        const emails = data.emails || [];
        const unreadCount = emails.filter((email: Email) => !email.isRead).length;

        console.log('📧 成功获取邮件数量:', emails.length);
        console.log('📧 邮件列表:', emails);

        set({
          emails,
          unreadCount,
          isLoading: false,
          error: null,
          lastFetchTime: Date.now(),
        });
      } else {
        console.log('📧 API返回失败:', data.message);
        set({
          error: data.message || '获取邮件失败',
          isLoading: false,
        });
      }
    } catch (error) {
      console.log('📧 网络错误:', error);
      set({
        error: '网络错误，请稍后重试',
        isLoading: false,
      });
    }
  },

  // 选择邮件
  selectEmail: (email: Email) => {
    set({ selectedEmail: email });
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      get().markAsRead(email.id);
    }
  },

  // 标记为已读
  markAsRead: (emailId: string) => {
    set((state) => ({
      emails: state.emails.map((email) =>
        email.id === emailId ? { ...email, isRead: true } : email
      ),
      unreadCount: Math.max(0, state.unreadCount - 1),
      selectedEmail: state.selectedEmail?.id === emailId 
        ? { ...state.selectedEmail, isRead: true }
        : state.selectedEmail,
    }));
  },

  // 标记为未读
  markAsUnread: (emailId: string) => {
    set((state) => ({
      emails: state.emails.map((email) =>
        email.id === emailId ? { ...email, isRead: false } : email
      ),
      unreadCount: state.unreadCount + 1,
      selectedEmail: state.selectedEmail?.id === emailId 
        ? { ...state.selectedEmail, isRead: false }
        : state.selectedEmail,
    }));
  },

  // 切换标星状态
  toggleStar: (emailId: string) => {
    set((state) => ({
      emails: state.emails.map((email) =>
        email.id === emailId ? { ...email, isStarred: !email.isStarred } : email
      ),
      selectedEmail: state.selectedEmail?.id === emailId 
        ? { ...state.selectedEmail, isStarred: !state.selectedEmail.isStarred }
        : state.selectedEmail,
    }));
  },

  // 删除邮件
  deleteEmail: (emailId: string) => {
    set((state) => {
      const emailToDelete = state.emails.find(email => email.id === emailId);
      const newUnreadCount = emailToDelete && !emailToDelete.isRead 
        ? Math.max(0, state.unreadCount - 1)
        : state.unreadCount;

      return {
        emails: state.emails.filter((email) => email.id !== emailId),
        selectedEmail: state.selectedEmail?.id === emailId ? null : state.selectedEmail,
        unreadCount: newUnreadCount,
      };
    });
  },

  // 设置当前文件夹
  setCurrentFolder: (folder: string) => {
    set({ currentFolder: folder, selectedEmail: null });
  },

  // 设置筛选条件
  setFilters: (filters: Partial<EmailFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
  },

  // 设置搜索查询
  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误信息
  setError: (error: string | null) => {
    set({ error });
  },

  // 刷新邮件
  refreshEmails: async (userEmail: string) => {
    await get().fetchEmails(userEmail);
  },

  // 设置自动刷新
  setAutoRefresh: (enabled: boolean) => {
    set({ autoRefreshEnabled: enabled });
  },

  // 设置刷新间隔
  setRefreshInterval: (seconds: number) => {
    set({ refreshInterval: seconds });
  },

  // 检查是否需要刷新
  shouldRefresh: () => {
    const { lastFetchTime, refreshInterval, autoRefreshEnabled } = get();

    if (!autoRefreshEnabled || !lastFetchTime) {
      return false;
    }

    const now = Date.now();
    const timeSinceLastFetch = (now - lastFetchTime) / 1000; // 转换为秒

    return timeSinceLastFetch >= refreshInterval;
  },
}));
