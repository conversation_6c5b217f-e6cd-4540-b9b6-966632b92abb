import { create } from 'zustand';
import type { VirtualUser, CreateUsersRequest } from '@/types';

interface AdminState {
  users: VirtualUser[];
  isLoading: boolean;
  error: string | null;
  totalUsers: number;
  domains: string[];
}

interface AdminActions {
  createUsers: (request: CreateUsersRequest) => Promise<VirtualUser[]>;
  fetchUsers: () => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  toggleUserStatus: (userId: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type AdminStore = AdminState & AdminActions;

export const useAdminStore = create<AdminStore>((set, get) => ({
  // 初始状态
  users: [],
  isLoading: false,
  error: null,
  totalUsers: 0,
  domains: [],

  // 创建用户
  createUsers: async (request: CreateUsersRequest) => {
    set({ isLoading: true, error: null });

    try {
      // 直接使用数据库服务
      const { db } = await import('@/lib/database');

      // 验证输入
      if (!request.domain || !request.count) {
        set({
          error: '域名和用户数量不能为空',
          isLoading: false,
        });
        return [];
      }

      if (request.count < 1 || request.count > 100) {
        set({
          error: '用户数量必须在1-100之间',
          isLoading: false,
        });
        return [];
      }

      // 验证域名格式（支持多级域名）
      const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(request.domain)) {
        set({
          error: '请输入有效的域名格式（如：example.com 或 sub.example.com）',
          isLoading: false,
        });
        return [];
      }

      const newUsers = await db.createUsers(request.domain, request.count);

      if (newUsers.length === 0) {
        set({
          error: '所有邮箱地址都已存在',
          isLoading: false,
        });
        return [];
      }

      set((state) => ({
        users: [...state.users, ...newUsers],
        totalUsers: state.totalUsers + newUsers.length,
        isLoading: false,
        error: null,
      }));

      return newUsers;
    } catch (error) {
      set({
        error: '创建用户失败',
        isLoading: false,
      });
      return [];
    }
  },

  // 获取用户列表
  fetchUsers: async () => {
    set({ isLoading: true, error: null });

    try {
      // 直接使用数据库服务
      const { db } = await import('@/lib/database');
      const users: VirtualUser[] = await db.getAllUsers();
      const domains: string[] = [...new Set(users.map((user: VirtualUser) =>
        user.email.split('@')[1]
      ))];

      set({
        users,
        totalUsers: users.length,
        domains,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        error: '获取用户列表失败',
        isLoading: false,
      });
    }
  },

  // 删除用户
  deleteUser: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // 直接使用数据库服务
      const { db } = await import('@/lib/database');
      const success = await db.deleteUser(userId);

      if (success) {
        set((state) => ({
          users: state.users.filter(user => user.id !== userId),
          totalUsers: Math.max(0, state.totalUsers - 1),
          isLoading: false,
          error: null,
        }));
      } else {
        set({
          error: '用户不存在',
          isLoading: false,
        });
      }
    } catch (error) {
      set({
        error: '删除用户失败',
        isLoading: false,
      });
    }
  },

  // 切换用户状态
  toggleUserStatus: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // 直接使用数据库服务
      const { db } = await import('@/lib/database');
      const updatedUser = await db.toggleUserStatus(userId);

      if (updatedUser) {
        set((state) => ({
          users: state.users.map(user =>
            user.id === userId
              ? { ...user, isActive: !user.isActive }
              : user
          ),
          isLoading: false,
          error: null,
        }));
      } else {
        set({
          error: '用户不存在',
          isLoading: false,
        });
      }
    } catch (error) {
      set({
        error: '切换用户状态失败',
        isLoading: false,
      });
    }
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误信息
  setError: (error: string | null) => {
    set({ error });
  },

  // 清除错误信息
  clearError: () => {
    set({ error: null });
  },
}));
