import { NextRequest, NextResponse } from 'next/server';
import { mailService, mailCache } from '@/lib/mail-service';
import { db } from '@/lib/database';
import type { EmailsResponse } from '@/types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userEmail: string }> }
) {
  try {
    const resolvedParams = await params;
    const userEmail = decodeURIComponent(resolvedParams.userEmail);

    // 验证用户是否存在
    const user = await db.getUserByEmail(userEmail);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在',
        emails: [],
        total: 0,
      } as EmailsResponse, { status: 404 });
    }

    if (!user.isActive) {
      return NextResponse.json({
        success: false,
        message: '用户账户已被禁用',
        emails: [],
        total: 0,
      } as EmailsResponse, { status: 403 });
    }

    // 尝试从缓存获取邮件
    let emails = mailCache.get(userEmail);

    if (!emails) {
      // 缓存中没有，从邮件服务获取
      emails = await mailService.fetchEmailsForUser(userEmail);
      
      // 存入缓存
      mailCache.set(userEmail, emails);
    }

    return NextResponse.json({
      success: true,
      emails,
      total: emails.length,
      message: '获取邮件成功',
    } as EmailsResponse);

  } catch (error) {
    console.error('获取邮件API错误:', error);
    return NextResponse.json({
      success: false,
      message: '获取邮件失败',
      emails: [],
      total: 0,
    } as EmailsResponse, { status: 500 });
  }
}

// 刷新邮件缓存
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userEmail: string }> }
) {
  try {
    const resolvedParams = await params;
    const userEmail = decodeURIComponent(resolvedParams.userEmail);

    // 验证用户是否存在
    const user = await db.getUserByEmail(userEmail);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在',
      }, { status: 404 });
    }

    // 清除缓存
    mailCache.clear(userEmail);

    // 重新获取邮件
    const emails = await mailService.fetchEmailsForUser(userEmail);
    
    // 存入缓存
    mailCache.set(userEmail, emails);

    return NextResponse.json({
      success: true,
      emails,
      total: emails.length,
      message: '邮件刷新成功',
    } as EmailsResponse);

  } catch (error) {
    console.error('刷新邮件API错误:', error);
    return NextResponse.json({
      success: false,
      message: '刷新邮件失败',
    }, { status: 500 });
  }
}
