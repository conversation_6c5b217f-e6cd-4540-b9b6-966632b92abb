import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import type { LoginRequest, LoginResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    const { email, password } = body;

    // 验证输入
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        message: '邮箱和密码不能为空',
      } as LoginResponse, { status: 400 });
    }

    // 验证用户
    const user = await db.validateUser(email, password);

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '邮箱或密码错误，或账户已被禁用',
      } as LoginResponse, { status: 401 });
    }

    // 生成简单的token（实际项目中应使用JWT）
    const token = Buffer.from(`${user.id}:${Date.now()}`).toString('base64');

    return NextResponse.json({
      success: true,
      user,
      token,
      message: '登录成功',
    } as LoginResponse);

  } catch (error) {
    console.error('登录API错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误',
    } as LoginResponse, { status: 500 });
  }
}
