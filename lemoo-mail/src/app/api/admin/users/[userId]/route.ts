import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import type { ApiResponse } from '@/types';

// 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    const success = await db.deleteUser(userId);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: '用户不存在',
      } as ApiResponse, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: '用户删除成功',
    } as ApiResponse);

  } catch (error) {
    console.error('删除用户API错误:', error);
    return NextResponse.json({
      success: false,
      error: '删除用户失败',
    } as ApiResponse, { status: 500 });
  }
}
