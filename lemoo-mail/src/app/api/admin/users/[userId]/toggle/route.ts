import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import type { ApiResponse, VirtualUser } from '@/types';

// 切换用户状态
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    const updatedUser = await db.toggleUserStatus(userId);

    if (!updatedUser) {
      return NextResponse.json({
        success: false,
        error: '用户不存在',
      } as ApiResponse, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: `用户状态已${updatedUser.isActive ? '激活' : '禁用'}`,
    } as ApiResponse<VirtualUser>);

  } catch (error) {
    console.error('切换用户状态API错误:', error);
    return NextResponse.json({
      success: false,
      error: '切换用户状态失败',
    } as ApiResponse, { status: 500 });
  }
}
