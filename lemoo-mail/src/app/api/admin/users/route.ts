import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import type { ApiResponse, VirtualUser } from '@/types';

export const dynamic = 'force-static';

// 获取所有用户
export async function GET(request: NextRequest) {
  try {
    const users = await db.getAllUsers();
    
    return NextResponse.json({
      success: true,
      data: { users },
      message: '获取用户列表成功',
    } as ApiResponse<{ users: VirtualUser[] }>);

  } catch (error) {
    console.error('获取用户列表API错误:', error);
    return NextResponse.json({
      success: false,
      error: '获取用户列表失败',
    } as ApiResponse, { status: 500 });
  }
}
