import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import type { CreateUsersRequest, CreateUsersResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: CreateUsersRequest = await request.json();
    const { domain, count, prefix = 'user' } = body;

    // 验证输入
    if (!domain || !count) {
      return NextResponse.json({
        success: false,
        users: [],
        message: '域名和用户数量不能为空',
      } as CreateUsersResponse, { status: 400 });
    }

    if (count < 1 || count > 100) {
      return NextResponse.json({
        success: false,
        users: [],
        message: '用户数量必须在1-100之间',
      } as CreateUsersResponse, { status: 400 });
    }

    // 验证域名格式
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(domain)) {
      return NextResponse.json({
        success: false,
        users: [],
        message: '请输入有效的域名格式',
      } as CreateUsersResponse, { status: 400 });
    }

    // 创建用户
    const newUsers = await db.createUsers(domain, count, prefix);

    if (newUsers.length === 0) {
      return NextResponse.json({
        success: false,
        users: [],
        message: '所有邮箱地址都已存在',
      } as CreateUsersResponse, { status: 409 });
    }

    return NextResponse.json({
      success: true,
      users: newUsers,
      message: `成功创建 ${newUsers.length} 个用户`,
    } as CreateUsersResponse);

  } catch (error) {
    console.error('创建用户API错误:', error);
    return NextResponse.json({
      success: false,
      users: [],
      message: '创建用户失败',
    } as CreateUsersResponse, { status: 500 });
  }
}
