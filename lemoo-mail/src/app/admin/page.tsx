'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminStore } from '@/store/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { VirtualUser, MarketingCard } from '@/types';

export default function AdminPage() {
  const router = useRouter();
  const {
    users,
    isLoading,
    error,
    totalUsers,
    domains,
    createUsers,
    fetchUsers,
    deleteUser,
    toggleUserStatus,
    clearError
  } = useAdminStore();

  const [createForm, setCreateForm] = useState({
    domain: '',
    count: 5,
  });

  // 保存的域名列表
  const [savedDomains, setSavedDomains] = useState<string[]>([]);
  const [hasUserClearedDomain, setHasUserClearedDomain] = useState(false);

  const [createdUsers, setCreatedUsers] = useState<VirtualUser[]>([]);
  const [showCreatedUsers, setShowCreatedUsers] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // 营销卡片状态
  const [marketingCards, setMarketingCards] = useState<MarketingCard[]>([]);
  const [showCardForm, setShowCardForm] = useState(false);
  const [editingCard, setEditingCard] = useState<MarketingCard | null>(null);
  const [cardForm, setCardForm] = useState({
    title: '',
    description: '',
    imageUrl: '',
    buttonText: '立即登录',
    buttonLink: '/login',
    isActive: true,
    order: 1,
  });

  // 🔐 管理员认证检查
  useEffect(() => {
    const checkAdminAuth = async () => {
      const token = localStorage.getItem('admin-token');

      if (!token) {
        router.push('/admin-login');
        return;
      }

      try {
        // 🔧 开发环境下使用Netlify Dev的端口
        const baseUrl = process.env.NODE_ENV === 'development'
          ? 'http://localhost:8888'
          : '';

        const response = await fetch(`${baseUrl}/.netlify/functions/admin-auth`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = await response.json();

        if (data.success) {
          setIsAuthenticated(true);
          fetchUsers();
        } else {
          localStorage.removeItem('admin-token');
          localStorage.removeItem('admin-user');
          router.push('/admin-login');
        }
      } catch (error) {
        console.error('管理员认证检查失败:', error);
        router.push('/admin-login');
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAdminAuth();
  }, [router, fetchUsers]);

  // 获取营销卡片和保存的域名
  useEffect(() => {
    const fetchMarketingCards = async () => {
      try {
        const { db } = await import('@/lib/database');
        const cards = await db.getAllMarketingCards();
        setMarketingCards(cards);
      } catch (error) {
        console.error('获取营销卡片失败:', error);
      }
    };

    // 加载保存的域名
    const loadSavedDomains = () => {
      const saved = localStorage.getItem('admin-saved-domains');
      if (saved) {
        const domains = JSON.parse(saved);
        setSavedDomains(domains);
        // 只在初始加载时设置默认域名，不在每次重新渲染时设置
        if (domains.length > 0 && !createForm.domain && !hasUserClearedDomain) {
          setCreateForm(prev => ({ ...prev, domain: domains[0] }));
        }
      }
    };

    if (isAuthenticated) {
      fetchMarketingCards();
      loadSavedDomains();
    }
  }, [isAuthenticated, createForm.domain]);

  // 显示加载状态
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">验证管理员权限...</p>
        </div>
      </div>
    );
  }

  // 未认证时不显示内容
  if (!isAuthenticated) {
    return null;
  }

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!createForm.domain) {
      return;
    }

    // 保存域名到历史记录
    saveDomain(createForm.domain);

    const newUsers = await createUsers({
      domain: createForm.domain,
      count: createForm.count,
    });

    if (newUsers.length > 0) {
      setCreatedUsers(newUsers);
      setShowCreatedUsers(true);
      // 保持域名不重置，只重置数量
      setCreateForm(prev => ({
        ...prev,
        count: 5,
      }));
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (confirm('确定要删除这个用户吗？')) {
      await deleteUser(userId);
    }
  };

  const handleToggleStatus = async (userId: string) => {
    await toggleUserStatus(userId);
  };

  // 保存域名到本地存储
  const saveDomain = (domain: string) => {
    if (!domain.trim()) return;

    const newDomains = [...new Set([domain.trim(), ...savedDomains])]; // 去重并置顶
    setSavedDomains(newDomains);
    localStorage.setItem('admin-saved-domains', JSON.stringify(newDomains));
  };

  // 删除保存的域名
  const removeSavedDomain = (domain: string) => {
    const newDomains = savedDomains.filter(d => d !== domain);
    setSavedDomains(newDomains);
    localStorage.setItem('admin-saved-domains', JSON.stringify(newDomains));
  };

  // 复制用户信息（格式化）
  const copyUserInfo = (user: VirtualUser) => {
    const formattedText = `邮箱：${user.email}
密码：${user.password}
状态：${user.isActive ? '激活' : '禁用'}`;

    navigator.clipboard.writeText(formattedText);

    // 显示复制成功提示
    const button = document.activeElement as HTMLButtonElement;
    if (button) {
      const originalText = button.textContent;
      button.textContent = '已复制';
      button.disabled = true;
      setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
      }, 2000);
    }
  };

  // 营销卡片管理函数
  const handleCardSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const { db } = await import('@/lib/database');

      if (editingCard) {
        // 更新卡片
        await db.updateMarketingCard(editingCard.id, cardForm);
      } else {
        // 创建新卡片
        await db.createMarketingCard(cardForm);
      }

      // 刷新卡片列表
      const cards = await db.getAllMarketingCards();
      setMarketingCards(cards);

      // 重置表单
      setCardForm({
        title: '',
        description: '',
        imageUrl: '',
        buttonText: '立即登录',
        buttonLink: '/login',
        isActive: true,
        order: 1,
      });
      setEditingCard(null);
      setShowCardForm(false);
    } catch (error) {
      console.error('保存营销卡片失败:', error);
    }
  };

  const handleEditCard = (card: MarketingCard) => {
    setCardForm({
      title: card.title,
      description: card.description,
      imageUrl: card.imageUrl || '',
      buttonText: card.buttonText,
      buttonLink: card.buttonLink,
      isActive: card.isActive,
      order: card.order,
    });
    setEditingCard(card);
    setShowCardForm(true);
  };

  const handleDeleteCard = async (cardId: string) => {
    if (!confirm('确定要删除这个营销卡片吗？')) {
      return;
    }

    try {
      const { db } = await import('@/lib/database');
      await db.deleteMarketingCard(cardId);

      // 刷新卡片列表
      const cards = await db.getAllMarketingCards();
      setMarketingCards(cards);
    } catch (error) {
      console.error('删除营销卡片失败:', error);
    }
  };

  // 管理员退出登录
  const handleLogout = () => {
    localStorage.removeItem('admin-token');
    localStorage.removeItem('admin-user');
    router.push('/admin-login');
  };

  const exportUsers = () => {
    const csvContent = [
      ['邮箱', '密码', '状态', '创建时间'],
      ...users.map(user => [
        user.email,
        user.password,
        user.isActive ? '激活' : '禁用',
        new Date(user.createdAt).toLocaleString('zh-CN')
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `users_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 顶部导航栏 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  LemooMail 管理控制台
                </h1>
                <p className="text-sm text-gray-600">域名邮箱服务管理平台</p>
              </div>
            </div>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="shadow-sm hover:shadow-md transition-all duration-200 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              退出登录
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8 space-y-8">

        {/* 统计信息仪表板 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700 mb-1">总用户数</p>
                  <p className="text-3xl font-bold text-blue-900">{totalUsers}</p>
                  <p className="text-xs text-blue-600 mt-1">已注册用户总数</p>
                </div>
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-emerald-700 mb-1">域名数量</p>
                  <p className="text-3xl font-bold text-emerald-900">{domains.length}</p>
                  <p className="text-xs text-emerald-600 mt-1">已配置域名总数</p>
                </div>
                <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-700 mb-1">激活用户</p>
                  <p className="text-3xl font-bold text-purple-900">
                    {users.filter(user => user.isActive).length}
                  </p>
                  <p className="text-xs text-purple-600 mt-1">可正常使用的用户</p>
                </div>
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* 创建用户表单 */}
          <Card className="xl:col-span-1 bg-white/70 backdrop-blur-sm shadow-xl border-0">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>批量创建用户</span>
              </CardTitle>
              <CardDescription className="text-blue-100">
                为指定域名快速创建邮箱账户
              </CardDescription>
            </CardHeader>

            <CardContent className="p-6">
              <form onSubmit={handleCreateSubmit} className="space-y-5">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    目标域名
                  </label>
                  <div className="relative">
                    <Input
                      value={createForm.domain}
                      onChange={(e) => {
                        const value = e.target.value;
                        setCreateForm(prev => ({ ...prev, domain: value }));
                        // 如果用户清空了输入框，标记为已清空
                        if (value === '') {
                          setHasUserClearedDomain(true);
                        } else {
                          setHasUserClearedDomain(false);
                        }
                      }}
                      placeholder="example.com"
                      className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      required
                      list="saved-domains"
                    />
                    <datalist id="saved-domains">
                      {savedDomains.map((domain) => (
                        <option key={domain} value={domain} />
                      ))}
                    </datalist>
                    {savedDomains.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-500 mb-1">常用域名：</p>
                        <div className="flex flex-wrap gap-1">
                          {savedDomains.slice(0, 3).map((domain) => (
                            <button
                              key={domain}
                              type="button"
                              onClick={() => {
                                setCreateForm(prev => ({ ...prev, domain }));
                                setHasUserClearedDomain(false); // 用户主动选择域名，重置清空状态
                              }}
                              className="inline-flex items-center px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors"
                            >
                              {domain}
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeSavedDomain(domain);
                                }}
                                className="ml-1 text-blue-500 hover:text-red-500"
                              >
                                ×
                              </button>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    创建数量
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="50"
                    value={createForm.count}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, count: parseInt(e.target.value) || 1 }))}
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-800">智能用户名生成</p>
                      <p className="text-xs text-blue-600 mt-1">
                        自动生成格式：6位字母+4位数字@{createForm.domain || 'domain.com'}
                      </p>
                      <p className="text-xs text-blue-500 mt-1">
                        示例：abcdef1234@{createForm.domain || 'domain.com'}
                      </p>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-sm text-red-700 font-medium">{error}</p>
                    </div>
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      创建中...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      创建用户
                    </div>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* 用户列表 */}
          <Card className="xl:col-span-2 bg-white/70 backdrop-blur-sm shadow-xl border-0">
            <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <span>用户管理</span>
                  </CardTitle>
                  <CardDescription className="text-emerald-100">
                    管理所有邮箱账户的状态和信息
                  </CardDescription>
                </div>
                <Button
                  onClick={exportUsers}
                  variant="outline"
                  size="sm"
                  className="bg-white/20 border-white/30 text-white hover:bg-white/30 hover:border-white/50"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  导出CSV
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="p-6">
              <div className="space-y-3 max-h-[500px] overflow-y-auto custom-scrollbar">
                {users.length === 0 ? (
                  <div className="text-center py-12">
                    <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <p className="text-gray-500 text-lg font-medium">暂无用户</p>
                    <p className="text-gray-400 text-sm mt-1">创建第一个用户开始使用</p>
                  </div>
                ) : (
                  users.map((user) => (
                    <div key={user.id} className="bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-sm">
                                {user.email.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div className="flex-1">
                              <p className="font-semibold text-gray-900 text-sm">{user.email}</p>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge
                                  variant={user.isActive ? "default" : "secondary"}
                                  className={`text-xs ${user.isActive ? 'bg-green-100 text-green-800 border-green-200' : 'bg-gray-100 text-gray-600 border-gray-200'}`}
                                >
                                  {user.isActive ? '✓ 已激活' : '○ 未激活'}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {user.email.split('@')[1]}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-3 mb-3">
                            <p className="text-xs text-gray-600 mb-2 flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6c-3 0-5.5-1.5-5.5-4a3.5 3.5 0 117 0c0 2.5-2.5 4-5.5 4a6 6 0 01-6-6 2 2 0 012-2m0 0V3" />
                              </svg>
                              登录密码
                            </p>
                            <p className="font-mono text-sm text-gray-800 bg-white px-2 py-1 rounded border">
                              {user.password}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-gray-500">
                            <div className="flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              创建: {user.createdAt.toLocaleString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                            {user.activatedAt && (
                              <div className="flex items-center text-green-600">
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                激活: {user.activatedAt.toLocaleString('zh-CN', {
                                  month: '2-digit',
                                  day: '2-digit',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-col space-y-2 ml-6">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyUserInfo(user)}
                            className="text-xs bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 shadow-sm"
                          >
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            复制
                          </Button>
                          <Button
                            size="sm"
                            variant={user.isActive ? "secondary" : "default"}
                            onClick={() => handleToggleStatus(user.id)}
                            className={`text-xs shadow-sm ${
                              user.isActive
                                ? 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 hover:border-orange-300'
                                : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300'
                            }`}
                          >
                            {user.isActive ? (
                              <>
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                                </svg>
                                取消激活
                              </>
                            ) : (
                              <>
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                激活
                              </>
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-xs bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 shadow-sm"
                          >
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            删除
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 营销卡片管理区域 */}
        <Card className="bg-white/70 backdrop-blur-sm shadow-xl border-0 mt-8">
          <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 3v1h6V3H9zM5 7v11h14V7H5z" />
                  </svg>
                  <span>首页营销卡片</span>
                </CardTitle>
                <CardDescription className="text-purple-100">
                  管理首页展示的营销推广内容
                </CardDescription>
              </div>
              <Button
                onClick={() => setShowCardForm(true)}
                className="bg-white/20 border-white/30 text-white hover:bg-white/30 hover:border-white/50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                添加卡片
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            <div className="space-y-4">
              {marketingCards.length === 0 ? (
                <div className="text-center py-12">
                  <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 3v1h6V3H9zM5 7v11h14V7H5z" />
                  </svg>
                  <p className="text-gray-500 text-lg font-medium">暂无营销卡片</p>
                  <p className="text-gray-400 text-sm mt-1">点击"添加卡片"创建第一个营销内容</p>
                </div>
              ) : (
                marketingCards.map((card) => (
                  <div key={card.id} className="bg-gradient-to-r from-white to-purple-50 border border-purple-200 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-sm">
                              {card.order}
                            </span>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 text-lg">{card.title}</h3>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge
                                variant={card.isActive ? "default" : "secondary"}
                                className={`text-xs ${card.isActive ? 'bg-green-100 text-green-800 border-green-200' : 'bg-gray-100 text-gray-600 border-gray-200'}`}
                              >
                                {card.isActive ? '✓ 已激活' : '○ 已禁用'}
                              </Badge>
                              <span className="text-xs text-gray-500">排序: {card.order}</span>
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-3 leading-relaxed">{card.description}</p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                            按钮文字: <span className="font-medium">{card.buttonText}</span>
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            跳转链接: <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{card.buttonLink}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2 ml-6">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditCard(card)}
                          className="text-xs bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 shadow-sm"
                        >
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          编辑
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteCard(card.id)}
                          className="text-xs bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 shadow-sm"
                        >
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          删除
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* 自定义样式 */}
        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
          }
        `}</style>

        {/* 新创建用户弹窗 */}
        {showCreatedUsers && createdUsers.length > 0 && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <Card className="max-w-3xl w-full max-h-[80vh] overflow-hidden shadow-2xl border-0">
              <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
                <CardTitle className="flex items-center space-x-2">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>用户创建成功</span>
                </CardTitle>
                <CardDescription className="text-green-100">
                  以下是新创建的用户账户信息，请妥善保存
                </CardDescription>
              </CardHeader>

              <CardContent className="p-6">
                <div className="space-y-3 max-h-64 overflow-y-auto custom-scrollbar">
                  {createdUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {user.email.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{user.email}</p>
                          <p className="text-sm text-gray-600">
                            密码: <span className="font-mono bg-white px-2 py-1 rounded border">{user.password}</span>
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyUserInfo(user)}
                        className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        复制
                      </Button>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex justify-end">
                  <Button
                    onClick={() => setShowCreatedUsers(false)}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    关闭
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 营销卡片表单弹窗 */}
        {showCardForm && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl border-0">
              <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                <CardTitle className="flex items-center space-x-2">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 3v1h6V3H9zM5 7v11h14V7H5z" />
                  </svg>
                  <span>{editingCard ? '编辑营销卡片' : '添加营销卡片'}</span>
                </CardTitle>
                <CardDescription className="text-purple-100">
                  配置首页展示的营销推广内容
                </CardDescription>
              </CardHeader>

              <CardContent className="p-6 max-h-[70vh] overflow-y-auto custom-scrollbar">
                <form onSubmit={handleCardSubmit} className="space-y-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">
                        卡片标题 *
                      </label>
                      <Input
                        value={cardForm.title}
                        onChange={(e) => setCardForm(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="输入吸引人的标题"
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">
                        按钮文字 *
                      </label>
                      <Input
                        value={cardForm.buttonText}
                        onChange={(e) => setCardForm(prev => ({ ...prev, buttonText: e.target.value }))}
                        placeholder="如：立即体验、了解更多"
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">
                      卡片描述 *
                    </label>
                    <textarea
                      className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                      rows={3}
                      value={cardForm.description}
                      onChange={(e) => setCardForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="详细描述这个营销内容的特点和优势..."
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">
                      跳转链接 *
                    </label>
                    <Input
                      value={cardForm.buttonLink}
                      onChange={(e) => setCardForm(prev => ({ ...prev, buttonLink: e.target.value }))}
                      placeholder="/login 或 https://example.com"
                      className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">
                      图片链接（可选）
                    </label>
                    <Input
                      value={cardForm.imageUrl}
                      onChange={(e) => setCardForm(prev => ({ ...prev, imageUrl: e.target.value }))}
                      placeholder="https://example.com/image.jpg"
                      className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">
                        显示排序
                      </label>
                      <Input
                        type="number"
                        min="1"
                        max="100"
                        value={cardForm.order}
                        onChange={(e) => setCardForm(prev => ({ ...prev, order: parseInt(e.target.value) || 1 }))}
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">
                        状态设置
                      </label>
                      <div className="flex items-center space-x-3 pt-2">
                        <input
                          type="checkbox"
                          id="cardIsActive"
                          checked={cardForm.isActive}
                          onChange={(e) => setCardForm(prev => ({ ...prev, isActive: e.target.checked }))}
                          className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                        />
                        <label htmlFor="cardIsActive" className="text-sm text-gray-700 font-medium">
                          立即激活显示
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-purple-800">营销卡片预览</p>
                        <p className="text-xs text-purple-600 mt-1">
                          卡片将按照排序顺序在首页展示，激活状态的卡片才会对用户可见
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <Button
                      type="submit"
                      className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {editingCard ? '更新卡片' : '创建卡片'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setShowCardForm(false);
                        setEditingCard(null);
                        setCardForm({
                          title: '',
                          description: '',
                          imageUrl: '',
                          buttonText: '立即登录',
                          buttonLink: '/login',
                          isActive: true,
                          order: 1,
                        });
                      }}
                      className="px-6 border-gray-300 hover:bg-gray-50"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      取消
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

      </div>
    </div>
  );
}
