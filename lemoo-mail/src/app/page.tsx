'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import MarketingCard from '@/components/marketing/MarketingCard';
import type { MarketingCard as MarketingCardType } from '@/types';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();
  const [marketingCards, setMarketingCards] = useState<MarketingCardType[]>([]);

  // 获取营销卡片
  useEffect(() => {
    const fetchMarketingCards = async () => {
      try {
        const { db } = await import('@/lib/database');
        const cards = await db.getActiveMarketingCards();

        if (cards.length === 0) {
          // 如果没有卡片，初始化默认卡片
          await db.initializeDefaultMarketingCards();
          const defaultCards = await db.getActiveMarketingCards();
          setMarketingCards(defaultCards);
        } else {
          setMarketingCards(cards);
        }
      } catch (error) {
        console.error('获取营销卡片失败:', error);
      }
    };

    fetchMarketingCards();
  }, []);

  // 用户认证重定向
  useEffect(() => {
    // 如果已登录，根据用户类型重定向
    if (isAuthenticated && user) {
      const isAdmin = user.email.toLowerCase().includes('admin') ||
                     user.email.toLowerCase().startsWith('admin@');

      if (isAdmin) {
        router.push('/admin');
      } else {
        router.push('/mail');
      }
    }
  }, [isAuthenticated, user, router]);

  const handleLogin = () => {
    router.push('/login');
  };

  return (
    <>
      {/* SEO Meta Tags */}
      <head>
        <title>账号验证服务 - 安全便捷的邮箱验证码接收平台</title>
        <meta name="description" content="专业的账号验证服务平台，提供安全可靠的邮箱验证码接收服务。支持多域名配置，快速获取验证码，保护您的隐私安全。" />
        <meta name="keywords" content="验证码接收,邮箱验证,账号验证,临时邮箱,验证码服务,隐私保护" />
        <meta name="author" content="Account Verification Service" />
        <meta property="og:title" content="账号验证服务 - 邮箱验证码接收平台" />
        <meta property="og:description" content="安全便捷的邮箱验证码接收服务，保护您的隐私" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="账号验证服务 - 邮箱验证码接收平台" />
        <meta name="twitter:description" content="安全便捷的邮箱验证码接收服务" />
      </head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          {/* 营销卡片区域 */}
          {marketingCards.length > 0 && (
            <div className="mb-8">
              {marketingCards.map((card) => (
                <MarketingCard key={card.id} card={card} />
              ))}
            </div>
          )}

          {/* 默认内容（如果没有营销卡片） */}
          {marketingCards.length === 0 && (
            <Card className="text-center">
              <CardContent className="p-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  账号验证服务
                </h1>
                <p className="text-gray-600 mb-6">
                  安全便捷的邮箱验证码接收平台
                </p>
                <Button
                  onClick={handleLogin}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
                >
                  登录接收验证码
                </Button>
              </CardContent>
            </Card>
          )}

        </div>
      </div>
    </>
  );
}
