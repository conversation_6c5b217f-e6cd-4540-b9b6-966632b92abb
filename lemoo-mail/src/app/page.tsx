'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import Image from 'next/image';
import {
  LightningIcon,
  EmailIcon,
  PaymentIcon,
  RocketIcon,
  RefreshIcon,
  CheckIcon,
  RobotIcon,
  LockIcon,
  DeveloperIcon,
  StudentIcon,
  CompanyIcon
} from '@/components/icons';

export default function Home() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);
  const router = useRouter();
  const { login } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsLoading(true);

    try {
      // 🔐 使用安全的Netlify Functions创建用户
      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:8888'
        : '';

      const response = await fetch(`${baseUrl}/.netlify/functions/create-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      const data = await response.json();

      if (data.success) {
        // 设置用户信息到store
        await login(data.user.email, data.user.password);

        // 跳转到邮件页面
        router.push('/mail');
      } else {
        alert(data.message || '创建用户失败');
      }
    } catch (error) {
      console.error('创建用户失败:', error);
      alert('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-cyan-500/10" />

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
      <div className="absolute top-40 right-20 w-72 h-72 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-8 left-40 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>

      <div className="relative z-10">
        {/* 导航栏 */}
        <nav className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <RobotIcon className="w-8 h-8 text-purple-400" />
              <span className="text-2xl font-bold text-white">LemooMail</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">功能特色</a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">价格对比</a>
              <a href="#faq" className="text-gray-300 hover:text-white transition-colors">常见问题</a>
            </div>
          </div>
        </nav>

        {/* 主要内容区域 */}
        <main className="container mx-auto px-6 py-12">
          {/* 英雄区域 */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full text-purple-300 text-sm mb-6">
              <LightningIcon className="w-4 h-4 mr-2" />
              专业AI编程助手账号租赁平台
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              AugmentCode
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                账号租赁
              </span>
            </h1>

            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              600次消息/月，独享账号，成品号即买即用，无需安装插件
              <br />
              专为开发者和企业打造的专业AI编程助手服务
            </p>

            {/* CTA按钮组 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="输入邮箱地址获取验证码"
                  className="flex-1 px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isLoading ? (
                    <RefreshIcon className="w-5 h-5 animate-spin mr-2" />
                  ) : (
                    <RocketIcon className="w-5 h-5 mr-2" />
                  )}
                  {isLoading ? '创建中...' : '立即开始使用'}
                </button>
              </form>
            </div>

            {/* 信任指标 */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-gray-400 text-sm">
              <div className="flex items-center">
                <CheckIcon className="w-4 h-4 text-green-400 mr-2" />
                即买即用
              </div>
              <div className="flex items-center">
                <LockIcon className="w-4 h-4 text-green-400 mr-2" />
                安全可靠
              </div>
              <div className="flex items-center">
                <PaymentIcon className="w-4 h-4 text-green-400 mr-2" />
                微信支付
              </div>
              <div className="flex items-center">
                <EmailIcon className="w-4 h-4 text-green-400 mr-2" />
                即时交付
              </div>
            </div>
          </div>

          {/* 功能特色区域 */}
          <section id="features" className="mb-20">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">为什么选择我们？</h2>
              <p className="text-gray-300 text-lg">专业的AugmentCode账号租赁服务，让您的开发效率翻倍</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* 功能卡片1 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mb-6">
                  <RocketIcon className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">即买即用</h3>
                <p className="text-gray-300">成品号直接交付，无需等待，无需安装插件，登录即可使用</p>
              </div>

              {/* 功能卡片2 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mb-6">
                  <LockIcon className="w-6 h-6 text-green-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">独享账号</h3>
                <p className="text-gray-300">600次消息/月，专属账号，不与他人共享，保证使用体验</p>
              </div>

              {/* 功能卡片3 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mb-6">
                  <PaymentIcon className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">微信支付</h3>
                <p className="text-gray-300">支持微信支付，安全便捷，即时交付，无需担心支付安全</p>
              </div>
            </div>
          </section>

          {/* 价格对比区域 */}
          <section id="pricing" className="mb-20">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-yellow-500/20 border border-yellow-400/30 rounded-full text-yellow-300 text-sm font-medium backdrop-blur-sm mb-6">
                <span className="mr-2">💰</span>
                官方定价对比
              </div>
              <h2 className="text-4xl font-bold text-white mb-4">价格对比</h2>
              <p className="text-gray-300 text-lg">看看我们的价格优势</p>
            </div>

            <div className="max-w-6xl mx-auto">
              <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-2xl"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-400/20 to-pink-500/20 rounded-full blur-xl"></div>

                <div className="relative z-10">
                  {/* 价格对比头部 */}
                  <div className="flex items-center justify-center space-x-8 mb-8">
                    {/* 划线价格 */}
                    <div className="text-center">
                      <div className="relative inline-block">
                        <span className="text-4xl font-bold text-gray-400 line-through animate-slash">¥299</span>
                        <div className="absolute inset-0 bg-red-500/20 transform rotate-12 rounded"></div>
                      </div>
                      <div className="text-sm text-red-300 mt-1">原价</div>
                    </div>

                    {/* 箭头 */}
                    <div className="text-3xl text-yellow-400 animate-pulse">→</div>

                    {/* 现价 */}
                    <div className="text-center">
                      <div className="text-5xl font-bold text-green-400 animate-price-drop">¥99</div>
                      <div className="text-sm text-green-300 mt-1">每月仅需</div>
                    </div>

                    {/* 节省信息 */}
                    <div className="bg-green-500/10 rounded-xl p-4 animate-savings-pulse">
                      <div className="text-center">
                        <div className="text-xl font-bold text-green-400">节省 ¥200</div>
                        <div className="text-green-300 text-sm">节省67%</div>
                      </div>
                    </div>
                  </div>

                  {/* 大图展示 */}
                  <div className="max-w-5xl mx-auto mb-8">
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                      <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:scale-105 transition-transform duration-300">
                        <Image
                          src="/price.jpg"
                          alt="AugmentCode官方定价截图"
                          width={1000}
                          height={600}
                          className="rounded-xl shadow-2xl w-full h-auto"
                        />
                      </div>
                    </div>
                    <div className="text-center mt-4">
                      <p className="text-white font-semibold text-lg mb-2">官方定价：$50美元/月</p>
                      <a
                        href="https://augmentcode.com/pricing"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 text-blue-300 hover:text-blue-200 transition-colors text-sm"
                      >
                        <span>🔗</span>
                        <span>查看官网定价</span>
                      </a>
                    </div>
                  </div>

                  {/* 优势对比 */}
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-white/5 rounded-xl">
                      <div className="text-2xl font-bold text-green-400 mb-2">节省67%</div>
                      <div className="text-green-300 text-sm">相比官方价格</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-xl">
                      <div className="text-2xl font-bold text-blue-400 mb-2">即买即用</div>
                      <div className="text-blue-300 text-sm">无需等待</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-xl">
                      <div className="text-2xl font-bold text-purple-400 mb-2">100%售后无忧</div>
                      <div className="text-purple-300 text-sm">专业客服支持</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 适用人群 */}
          <section className="mb-20">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">适用人群</h2>
              <p className="text-gray-300 text-lg">无论您是个人开发者还是企业团队，我们都有适合的方案</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* 开发者 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 text-center hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <DeveloperIcon className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">个人开发者</h3>
                <p className="text-gray-300">提升编程效率，快速解决技术难题，让AI成为您的编程伙伴</p>
              </div>

              {/* 学生 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 text-center hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <StudentIcon className="w-8 h-8 text-green-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">学生群体</h3>
                <p className="text-gray-300">学习编程更轻松，作业项目不再难，AI助手陪伴您的学习之路</p>
              </div>

              {/* 企业 */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 text-center hover:bg-white/10 transition-all duration-300">
                <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CompanyIcon className="w-8 h-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">企业团队</h3>
                <p className="text-gray-300">团队协作更高效，代码质量更优秀，降低开发成本提升竞争力</p>
              </div>
            </div>
          </section>

          {/* FAQ区域 */}
          <section id="faq" className="mb-20">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 border border-blue-400/30 rounded-full text-blue-300 text-sm font-medium backdrop-blur-sm mb-6">
                <span className="mr-2">❓</span>
                常见问题
              </div>
              <h2 className="text-4xl font-bold text-white mb-4">常见问题</h2>
              <p className="text-gray-300 text-lg">解答您可能遇到的疑问</p>
            </div>

            <div className="max-w-4xl mx-auto space-y-4">
              {[
                {
                  question: "接收不到验证码怎么办？",
                  answer: (
                    <div>
                      <p className="text-blue-200 text-sm leading-relaxed mb-3">
                        请联系客服检查邮箱是否填写错误，我们的客服团队将为您提供专业帮助。
                      </p>
                      <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/20">
                        <p className="text-blue-300 text-sm">
                          <span className="font-medium">客服邮箱：</span>
                          <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                  )
                },
                {
                  question: "账号安全性如何保障？",
                  answer: (
                    <div>
                      <p className="text-blue-200 text-sm leading-relaxed mb-3">
                        我们提供的都是正版AugmentCode账号，通过官方渠道获取，确保账号安全性和稳定性。
                      </p>
                      <ul className="text-blue-200 text-sm space-y-1">
                        <li>• 官方正版账号，非破解版本</li>
                        <li>• 独享使用，不与他人共享</li>
                        <li>• 定期更新维护，确保稳定性</li>
                      </ul>
                    </div>
                  )
                },
                {
                  question: "如何使用租赁的账号？",
                  answer: (
                    <div>
                      <p className="text-blue-200 text-sm leading-relaxed mb-3">
                        购买后我们会提供账号密码，您可以直接登录AugmentCode官网使用，无需安装任何插件。
                      </p>
                      <div className="p-3 bg-green-500/10 rounded-lg border border-green-400/20">
                        <p className="text-green-300 text-sm">
                          <span className="font-medium">使用步骤：</span>
                          1. 收到账号信息 → 2. 登录官网 → 3. 开始使用
                        </p>
                      </div>
                    </div>
                  )
                },
                {
                  question: "600次消息够用吗？",
                  answer: (
                    <p className="text-blue-200 text-sm leading-relaxed">
                      600次消息对于大多数开发者来说是足够的，相当于每天20次使用，可以满足日常开发需求。
                      如果您是重度用户，我们也提供更高配置的套餐。
                    </p>
                  )
                },
                {
                  question: "支持退款吗？",
                  answer: (
                    <p className="text-blue-200 text-sm leading-relaxed">
                      由于是数字产品，一旦发送账号信息后不支持退款。购买前请确认您的需求和邮箱地址。
                    </p>
                  )
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-300">
                  <button
                    onClick={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                  >
                    <div className="flex items-center">
                      <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">Q</span>
                      <h3 className="text-lg font-semibold text-white">{faq.question}</h3>
                    </div>
                    <div className={`transform transition-transform duration-300 ${expandedFAQ === index ? 'rotate-180' : ''}`}>
                      <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${expandedFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                    <div className="px-6 pb-6">
                      {faq.answer}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="border-t border-white/10 py-8">
          <div className="container mx-auto px-6">
            <div className="text-center">
              <p className="text-sm text-gray-500">&copy; 2024 LemooMail. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
