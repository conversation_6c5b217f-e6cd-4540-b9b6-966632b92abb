'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { isValidEmail } from '@/lib/utils';

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useAuthStore();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
  });

  const [rememberEmail, setRememberEmail] = useState(true);
  const [savedEmails, setSavedEmails] = useState<string[]>([]);

  // 加载保存的邮箱地址
  useEffect(() => {
    const saved = localStorage.getItem('lemoo-saved-emails');
    if (saved) {
      const emails = JSON.parse(saved);
      setSavedEmails(emails);
      // 如果有保存的邮箱，默认选择最近使用的
      if (emails.length > 0) {
        setFormData(prev => ({ ...prev, email: emails[0] }));
      }
    }
  }, []);

  // 保存邮箱地址
  const saveEmail = (email: string) => {
    if (!rememberEmail || !email.trim()) return;

    const newEmails = [email, ...savedEmails.filter(e => e !== email)].slice(0, 5); // 最多保存5个
    setSavedEmails(newEmails);
    localStorage.setItem('lemoo-saved-emails', JSON.stringify(newEmails));
  };

  // 删除保存的邮箱
  const removeSavedEmail = (email: string) => {
    const newEmails = savedEmails.filter(e => e !== email);
    setSavedEmails(newEmails);
    localStorage.setItem('lemoo-saved-emails', JSON.stringify(newEmails));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除对应字段的错误
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) {
      clearError();
    }
  };

  const validateForm = () => {
    const errors = { email: '', password: '' };
    let isValid = true;

    if (!formData.email) {
      errors.email = '请输入邮箱地址';
      isValid = false;
    } else if (!isValidEmail(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
      isValid = false;
    }

    if (!formData.password) {
      errors.password = '请输入密码';
      isValid = false;
    } else if (formData.password.length < 6) {
      errors.password = '密码长度至少6位';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const success = await login(formData.email, formData.password);

    if (success) {
      // 保存邮箱地址
      saveEmail(formData.email);
      router.push('/mail');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            LemooMail
          </h1>
          <p className="text-gray-600">
            轻量级邮件管理系统
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>登录您的邮箱</CardTitle>
            <CardDescription>
              请输入您的邮箱和密码
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱地址
                </label>
                <div className="relative">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className={formErrors.email ? 'border-red-500' : ''}
                    list="saved-emails"
                  />
                  <datalist id="saved-emails">
                    {savedEmails.map((email) => (
                      <option key={email} value={email} />
                    ))}
                  </datalist>

                  {savedEmails.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 mb-1">最近使用：</p>
                      <div className="flex flex-wrap gap-1">
                        {savedEmails.slice(0, 3).map((email) => (
                          <button
                            key={email}
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, email }))}
                            className="inline-flex items-center px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors"
                          >
                            {email}
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeSavedEmail(email);
                              }}
                              className="ml-1 text-blue-500 hover:text-red-500"
                            >
                              ×
                            </button>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                {formErrors.email && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  密码
                </label>
                <PasswordInput
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="请输入密码"
                  className={formErrors.password ? 'border-red-500' : ''}
                  required
                />
                {formErrors.password && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-email"
                    type="checkbox"
                    checked={rememberEmail}
                    onChange={(e) => setRememberEmail(e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="remember-email" className="ml-2 text-sm text-gray-700">
                    记住邮箱地址
                  </label>
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            需要创建邮箱账户？请联系管理员
          </p>
        </div>
      </div>
    </div>
  );
}
