'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { useMailStore } from '@/store/mail';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/lib/utils';
import type { Email } from '@/types';

export default function MailPage() {
  const router = useRouter();
  const { isAuthenticated, user, logout } = useAuthStore();
  const { emails, fetchEmails, isLoading } = useMailStore();
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [autoRefreshCount, setAutoRefreshCount] = useState(0);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [lastEmailCount, setLastEmailCount] = useState(0);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    // 🔐 改进的认证检查逻辑，类似管理员页面
    const checkUserAuth = async () => {
      try {
        // 首先检查localStorage中的持久化数据
        const authStorage = localStorage.getItem('auth-storage');

        if (!authStorage) {
          router.push('/login');
          return;
        }

        const parsed = JSON.parse(authStorage);
        const storedUser = parsed.state?.user;
        const storedToken = parsed.state?.token;
        const storedIsAuthenticated = parsed.state?.isAuthenticated;

        if (!storedUser || !storedToken || !storedIsAuthenticated) {
          router.push('/login');
          return;
        }

        // 检查是否为管理员，如果是则重定向到管理员面板
        const isAdmin = storedUser.email.toLowerCase().includes('admin') ||
                       storedUser.email.toLowerCase().startsWith('admin@');

        if (isAdmin) {
          router.push('/admin');
          return;
        }

        // 如果Zustand状态还没有恢复，等待一下
        if (!isAuthenticated || !user) {
          // 给Zustand一些时间来恢复状态
          setTimeout(() => {
            setIsCheckingAuth(false);
          }, 100);
          return;
        }

        setIsCheckingAuth(false);
      } catch (error) {
        console.error('用户认证检查失败:', error);
        router.push('/login');
      }
    };

    checkUserAuth();
  }, [isAuthenticated, user, router]);

  // 只有在认证检查完成且用户已认证时才加载邮件
  useEffect(() => {
    if (isCheckingAuth || !isAuthenticated || !user) {
      return;
    }

    // 获取邮件数据
    const loadEmails = async () => {
      setIsInitialLoading(true);
      await fetchEmails(user.email);
      setIsInitialLoading(false);
    };

    loadEmails();
  }, [isAuthenticated, user, fetchEmails]);

  // 智能自动刷新逻辑
  useEffect(() => {
    if (!user || !isAuthenticated || !autoRefreshEnabled) return;

    let timeoutId: NodeJS.Timeout;

    const performAutoRefresh = async () => {
      const previousCount = emails.length;
      await fetchEmails(user.email);

      // 检查邮件数量是否变化
      if (emails.length !== previousCount) {
        console.log('检测到新邮件，停止自动刷新');
        setAutoRefreshEnabled(false);
        setLastEmailCount(emails.length);
        return;
      }

      setAutoRefreshCount(prev => prev + 1);

      // 渐进式间隔策略
      let nextInterval: number;
      if (autoRefreshCount < 50) { // 5分钟高频
        nextInterval = 6000;
      } else if (autoRefreshCount < 150) { // 15分钟中频
        nextInterval = 15000;
      } else { // 低频持续
        nextInterval = 30000;
      }

      // 继续下一次刷新
      timeoutId = setTimeout(performAutoRefresh, nextInterval);
    };

    // 初始10秒延迟后开始自动刷新
    timeoutId = setTimeout(() => {
      performAutoRefresh();
    }, 10000);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [user, isAuthenticated, fetchEmails, autoRefreshCount, emails.length, autoRefreshEnabled]);

  // 手动刷新邮件
  const handleRefresh = async () => {
    if (!user || isRefreshing) return;

    setIsRefreshing(true);
    const previousCount = emails.length;
    console.log('🔄 手动刷新开始 - 当前邮件数量:', previousCount);

    try {
      await fetchEmails(user.email);

      // 🔧 修复：使用setTimeout确保状态更新后再检查
      setTimeout(() => {
        const { emails: currentEmails } = useMailStore.getState();
        console.log('🔄 手动刷新完成 - 之前数量:', previousCount, '当前数量:', currentEmails.length);
        console.log('🔄 当前邮件列表:', currentEmails);

        // 检查邮件数量是否变化
        if (currentEmails.length === previousCount) {
          // 数量没有变化，重新启用自动刷新
          setAutoRefreshEnabled(true);
          setAutoRefreshCount(0); // 重置计数器
          console.log('手动刷新无新邮件，重新启用自动刷新');
        } else {
          // 数量发生变化，保持自动刷新关闭
          setAutoRefreshEnabled(false);
          setLastEmailCount(currentEmails.length);
          console.log('手动刷新检测到邮件变化，保持自动刷新关闭');
        }
      }, 100);
    } finally {
      setIsRefreshing(false);
    }
  };



  // 如果正在检查认证或未认证，显示加载状态
  if (isCheckingAuth || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-500">验证登录状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 专业顶部栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">邮箱验证码</h1>
              <p className="text-sm text-gray-500">{user.email}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              size="sm"
              className="border-gray-300 hover:border-blue-500 hover:text-blue-600"
            >
              {isRefreshing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-600 mr-2"></div>
                  刷新中
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  刷新
                </>
              )}
            </Button>
            <Button onClick={logout} variant="outline" size="sm" className="border-gray-300 hover:border-red-500 hover:text-red-600">
              退出
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-6 py-6">
        {/* 邮件接收盒子 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* 盒子头部 */}
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">{emails.length}</span>
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">收件箱</h2>
                    <p className="text-sm text-gray-500">
                      {isInitialLoading ? '正在加载邮件...' :
                       autoRefreshEnabled && autoRefreshCount > 0 ?
                         autoRefreshCount < 50 ? `自动刷新中 (高频模式)` :
                         autoRefreshCount < 150 ? `自动刷新中 (中频模式)` :
                         `自动刷新中 (低频模式)` :
                       `共 ${emails.length} 封邮件`}
                    </p>
                  </div>
                </div>
              </div>

              {/* 状态指示器 */}
              <div className="flex items-center space-x-3">
                {(isInitialLoading || isRefreshing) && (
                  <div className="flex items-center space-x-2 text-blue-600">
                    <div className="relative">
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-200 border-t-blue-600"></div>
                      <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-blue-400 animate-ping"></div>
                    </div>
                    <span className="text-sm font-medium">刷新中</span>
                  </div>
                )}
                {autoRefreshEnabled && autoRefreshCount > 0 && !isRefreshing && (
                  <div className="flex items-center space-x-2 text-green-600">
                    <div className="relative">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div className="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
                    </div>
                    <span className="text-sm font-medium">
                      {autoRefreshCount < 50 ? '高频监控' :
                       autoRefreshCount < 150 ? '中频监控' : '低频监控'}
                    </span>
                  </div>
                )}
                {!autoRefreshEnabled && emails.length > 0 && !isRefreshing && (
                  <div className="flex items-center space-x-2 text-gray-500">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span className="text-sm font-medium">监控已停止</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 邮件列表内容 */}
          <div className="divide-y divide-gray-100">
            {isInitialLoading ? (
              <div className="p-12 text-center">
                <div className="relative mx-auto mb-6">
                  <div className="animate-spin rounded-full h-12 w-12 border-3 border-gray-200 border-t-blue-600 mx-auto"></div>
                  <div className="absolute inset-0 rounded-full border-3 border-transparent border-t-blue-400 animate-ping"></div>
                </div>
                <p className="text-gray-600 font-medium">正在加载邮件...</p>
                <p className="text-gray-400 text-sm mt-1">请稍候</p>
              </div>
            ) : emails.length === 0 ? (
              <div className="p-16 text-center">
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">等待邮件到达</h3>
                <p className="text-gray-500 mb-6 max-w-sm mx-auto">使用此邮箱地址接收验证码，系统将自动检查新邮件</p>
                <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                  自动监控中
                </div>
              </div>
            ) : (
              emails.map((email) => (
                <div
                  key={email.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    selectedEmail?.id === email.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => setSelectedEmail(email)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-xs font-bold">
                            {email.from.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {email.from}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(email.date)}
                          </p>
                        </div>
                        {!email.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1 truncate">
                        {email.subject}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                        {email.body.replace(/\n/g, ' ').substring(0, 120)}...
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 邮件详情弹窗 */}
        {selectedEmail && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden border border-gray-200">
              {/* 详情头部 */}
              <div className="bg-gray-50 border-b border-gray-200 p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-xl font-semibold text-gray-900 mb-3 leading-tight">
                      {selectedEmail.subject}
                    </h2>
                    <div className="space-y-2 text-gray-600 text-sm">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                        <span className="font-medium">发件人：</span>
                        <span className="ml-1">{selectedEmail.from}</span>
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-medium">时间：</span>
                        <span className="ml-1">{formatDate(selectedEmail.date)}</span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedEmail(null)}
                    className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* 详情内容 */}
              <div className="p-6 max-h-96 overflow-y-auto">
                <div className="prose max-w-none">
                  <div className="whitespace-pre-wrap text-gray-900 leading-relaxed text-sm">
                    {selectedEmail.body}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
