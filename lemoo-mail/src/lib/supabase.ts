// Supabase数据库集成
import { createClient } from '@supabase/supabase-js';
import type { VirtualUser, MarketingCard } from '@/types';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseKey);

// 数据库表结构
export interface DatabaseUser {
  id: string;
  email: string;
  password: string;
  is_active: boolean;
  created_at: string;
  activated_at?: string;
}

export interface DatabaseMarketingCard {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  button_text: string;
  button_link: string;
  is_active: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
}

// 用户数据库操作
export class SupabaseUserService {
  // 获取所有用户
  async getAllUsers(): Promise<VirtualUser[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取用户失败:', error);
      return [];
    }

    return data.map(this.transformUser);
  }

  // 创建用户
  async createUser(user: Omit<VirtualUser, 'id'>): Promise<VirtualUser | null> {
    const { data, error } = await supabase
      .from('users')
      .insert({
        email: user.email,
        password: user.password,
        is_active: user.isActive,
        created_at: user.createdAt.toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('创建用户失败:', error);
      return null;
    }

    return this.transformUser(data);
  }

  // 批量创建用户
  async createUsers(users: Omit<VirtualUser, 'id'>[]): Promise<VirtualUser[]> {
    const { data, error } = await supabase
      .from('users')
      .insert(
        users.map(user => ({
          email: user.email,
          password: user.password,
          is_active: user.isActive,
          created_at: user.createdAt.toISOString(),
        }))
      )
      .select();

    if (error) {
      console.error('批量创建用户失败:', error);
      return [];
    }

    return data.map(this.transformUser);
  }

  // 更新用户状态
  async updateUserStatus(id: string, isActive: boolean): Promise<VirtualUser | null> {
    const updates: Record<string, unknown> = { is_active: isActive };
    
    // 如果是激活操作，记录激活时间
    if (isActive) {
      updates.activated_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('更新用户状态失败:', error);
      return null;
    }

    return this.transformUser(data);
  }

  // 删除用户
  async deleteUser(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除用户失败:', error);
      return false;
    }

    return true;
  }

  // 验证用户登录
  async validateUser(email: string, password: string): Promise<VirtualUser | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .eq('password', password)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      return null;
    }

    return this.transformUser(data);
  }

  // 获取统计信息
  async getStats() {
    const { data: users } = await supabase
      .from('users')
      .select('email, is_active');

    if (!users) return { totalUsers: 0, totalDomains: 0, activeUsers: 0 };

    const domains = new Set(users.map(u => u.email.split('@')[1]));
    const activeUsers = users.filter(u => u.is_active).length;

    return {
      totalUsers: users.length,
      totalDomains: domains.size,
      activeUsers,
    };
  }

  // 转换数据库用户为应用用户
  private transformUser(dbUser: DatabaseUser): VirtualUser {
    return {
      id: dbUser.id,
      email: dbUser.email,
      password: dbUser.password,
      isActive: dbUser.is_active,
      createdAt: new Date(dbUser.created_at),
      activatedAt: dbUser.activated_at ? new Date(dbUser.activated_at) : undefined,
    };
  }
}

// 营销卡片数据库操作
export class SupabaseMarketingService {
  // 获取所有营销卡片
  async getAllCards(): Promise<MarketingCard[]> {
    const { data, error } = await supabase
      .from('marketing_cards')
      .select('*')
      .order('order_index', { ascending: true });

    if (error) {
      console.error('获取营销卡片失败:', error);
      return [];
    }

    return data.map(this.transformCard);
  }

  // 获取激活的营销卡片
  async getActiveCards(): Promise<MarketingCard[]> {
    const { data, error } = await supabase
      .from('marketing_cards')
      .select('*')
      .eq('is_active', true)
      .order('order_index', { ascending: true });

    if (error) {
      console.error('获取激活营销卡片失败:', error);
      return [];
    }

    return data.map(this.transformCard);
  }

  // 创建营销卡片
  async createCard(card: Omit<MarketingCard, 'id' | 'createdAt' | 'updatedAt'>): Promise<MarketingCard | null> {
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('marketing_cards')
      .insert({
        title: card.title,
        description: card.description,
        image_url: card.imageUrl,
        button_text: card.buttonText,
        button_link: card.buttonLink,
        is_active: card.isActive,
        order_index: card.order,
        created_at: now,
        updated_at: now,
      })
      .select()
      .single();

    if (error) {
      console.error('创建营销卡片失败:', error);
      return null;
    }

    return this.transformCard(data);
  }

  // 更新营销卡片
  async updateCard(id: string, updates: Partial<Omit<MarketingCard, 'id' | 'createdAt'>>): Promise<MarketingCard | null> {
    const dbUpdates: Record<string, unknown> = {
      updated_at: new Date().toISOString(),
    };

    if (updates.title !== undefined) dbUpdates.title = updates.title;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.imageUrl !== undefined) dbUpdates.image_url = updates.imageUrl;
    if (updates.buttonText !== undefined) dbUpdates.button_text = updates.buttonText;
    if (updates.buttonLink !== undefined) dbUpdates.button_link = updates.buttonLink;
    if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;
    if (updates.order !== undefined) dbUpdates.order_index = updates.order;

    const { data, error } = await supabase
      .from('marketing_cards')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('更新营销卡片失败:', error);
      return null;
    }

    return this.transformCard(data);
  }

  // 删除营销卡片
  async deleteCard(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('marketing_cards')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除营销卡片失败:', error);
      return false;
    }

    return true;
  }

  // 转换数据库卡片为应用卡片
  private transformCard(dbCard: DatabaseMarketingCard): MarketingCard {
    return {
      id: dbCard.id,
      title: dbCard.title,
      description: dbCard.description,
      imageUrl: dbCard.image_url,
      buttonText: dbCard.button_text,
      buttonLink: dbCard.button_link,
      isActive: dbCard.is_active,
      order: dbCard.order_index,
      createdAt: new Date(dbCard.created_at),
      updatedAt: new Date(dbCard.updated_at),
    };
  }
}

// 导出服务实例
export const userService = new SupabaseUserService();
export const marketingService = new SupabaseMarketingService();
