import type { Email, EmailAttachment } from '@/types';
import { parseOriginalRecipient, generateId } from '@/lib/utils';

// 邮件服务配置
interface MailConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
}

// 模拟的IMAP邮件数据结构
interface RawEmail {
  uid: number;
  envelope: {
    from: Array<{ address: string; name?: string }>;
    to: Array<{ address: string; name?: string }>;
    subject: string;
    date: Date;
  };
  source: string;
  bodyStructure: unknown;
}

// 邮件服务类
export class MailService {
  private config: MailConfig;

  constructor(config: MailConfig) {
    this.config = config;
  }

  // 获取163邮箱的所有邮件
  async fetchAllEmails(): Promise<Email[]> {
    try {
      // 在静态部署中，我们需要模拟IMAP连接
      // 实际部署时，这里应该使用真实的IMAP库如imapflow
      
      // 模拟邮件数据（开发阶段）
      const mockEmails = this.generateMockEmails();
      
      return mockEmails;
    } catch (error) {
      console.error('获取邮件失败:', error);
      throw new Error('无法连接到邮件服务器');
    }
  }

  // 根据用户邮箱筛选邮件
  async fetchEmailsForUser(userEmail: string): Promise<Email[]> {
    const allEmails = await this.fetchAllEmails();
    
    // 筛选属于该用户的邮件
    return allEmails.filter(email => 
      email.to.toLowerCase() === userEmail.toLowerCase()
    );
  }

  // 解析原始邮件数据
  private parseRawEmail(rawEmail: RawEmail): Email {
    const originalTo = parseOriginalRecipient(rawEmail.source);
    
    return {
      id: generateId(),
      uid: rawEmail.uid,
      to: originalTo || rawEmail.envelope.to[0]?.address || '',
      from: rawEmail.envelope.from[0]?.address || '',
      subject: rawEmail.envelope.subject || '(无主题)',
      date: rawEmail.envelope.date,
      body: this.extractEmailBody(rawEmail),
      isRead: false,
      isStarred: false,
      attachments: this.extractAttachments(),
    };
  }

  // 提取邮件正文
  private extractEmailBody(rawEmail: RawEmail): string {
    // 简化的邮件正文提取
    // 实际实现中需要解析MIME结构
    const lines = rawEmail.source.split('\n');
    let bodyStartIndex = -1;
    
    // 找到邮件头部结束的位置
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() === '') {
        bodyStartIndex = i + 1;
        break;
      }
    }
    
    if (bodyStartIndex === -1) {
      return '无法解析邮件内容';
    }
    
    const bodyLines = lines.slice(bodyStartIndex);
    return bodyLines.join('\n').trim();
  }

  // 提取附件信息
  private extractAttachments(): EmailAttachment[] {
    // 简化的附件提取
    // 实际实现中需要解析MIME结构
    return [];
  }

  // 生成模拟邮件数据（开发阶段使用）
  private generateMockEmails(): Email[] {
    const domains = ['example.com', 'test.com', 'demo.com'];
    const senders = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    const subjects = [
      '欢迎使用我们的服务',
      '重要通知：账户安全更新',
      '您的订单已确认',
      '月度报告已生成',
      '系统维护通知',
      '新功能发布公告',
      '密码重置请求',
      '账单提醒'
    ];

    const emails: Email[] = [];
    
    // 为每个域名生成一些邮件
    domains.forEach(domain => {
      for (let i = 1; i <= 5; i++) {
        const userEmail = `user${i}@${domain}`;
        
        // 为每个用户生成3-8封邮件
        const emailCount = Math.floor(Math.random() * 6) + 3;
        
        for (let j = 0; j < emailCount; j++) {
          const sender = senders[Math.floor(Math.random() * senders.length)];
          const subject = subjects[Math.floor(Math.random() * subjects.length)];
          const daysAgo = Math.floor(Math.random() * 30);
          const date = new Date();
          date.setDate(date.getDate() - daysAgo);
          
          emails.push({
            id: generateId(),
            uid: emails.length + 1,
            to: userEmail,
            from: sender,
            subject: subject,
            date: date,
            body: this.generateMockEmailBody(subject),
            isRead: Math.random() > 0.3, // 70%的邮件已读
            isStarred: Math.random() > 0.8, // 20%的邮件标星
            attachments: [],
          });
        }
      }
    });

    // 按日期排序（最新的在前）
    return emails.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  // 生成模拟邮件正文
  private generateMockEmailBody(subject: string): string {
    const templates = {
      '欢迎使用我们的服务': `
        亲爱的用户，

        欢迎使用LemooMail邮件服务！

        我们很高兴您选择了我们的服务。您现在可以：
        - 接收和管理邮件
        - 使用简洁的界面
        - 享受快速的邮件体验

        如有任何问题，请随时联系我们。

        祝好，
        LemooMail团队
      `,
      '重要通知：账户安全更新': `
        尊敬的用户，

        为了提高您账户的安全性，我们已经更新了安全策略。

        主要变更包括：
        - 增强的密码要求
        - 新的登录验证机制
        - 改进的数据加密

        请确保您的账户信息是最新的。

        谢谢您的配合。
      `,
      default: `
        这是一封测试邮件。

        邮件内容：${subject}

        发送时间：${new Date().toLocaleString('zh-CN')}

        这是邮件正文的示例内容。在实际使用中，这里会显示真实的邮件内容。
      `
    };

    return templates[subject as keyof typeof templates] || templates.default;
  }
}

// 🚨 安全警告：不要在客户端代码中硬编码敏感信息！
// 这个配置应该移到服务端 Netlify Functions 中
export const mailService = new MailService({
  host: 'imap.163.com',
  port: 993,
  secure: true,
  user: '', // 🚨 已移除硬编码的邮箱
  password: '', // 🚨 已移除硬编码的密码
});

// 邮件缓存管理
class MailCache {
  private cache = new Map<string, { emails: Email[]; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  get(userEmail: string): Email[] | null {
    const cached = this.cache.get(userEmail);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(userEmail);
      return null;
    }

    return cached.emails;
  }

  set(userEmail: string, emails: Email[]): void {
    this.cache.set(userEmail, {
      emails,
      timestamp: Date.now(),
    });
  }

  clear(userEmail?: string): void {
    if (userEmail) {
      this.cache.delete(userEmail);
    } else {
      this.cache.clear();
    }
  }
}

export const mailCache = new MailCache();
