import type { VirtualUser, MarketingCard } from '@/types';
import { generateId, generateRandomPassword, generateRandomUsername } from '@/lib/utils';

// 数据库接口
interface Database {
  users: VirtualUser[];
  marketingCards: MarketingCard[];
}

// 本地存储键名
const DB_KEY = 'lemoo-mail-db';

// 数据库服务类
export class DatabaseService {
  private data: Database;

  constructor() {
    this.data = this.loadFromStorage();
  }

  // 从本地存储加载数据
  private loadFromStorage(): Database {
    if (typeof window === 'undefined') {
      return { users: [], marketingCards: [] };
    }

    try {
      const stored = localStorage.getItem(DB_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // 确保日期字段正确解析
        parsed.users = parsed.users.map((user: VirtualUser) => ({
          ...user,
          createdAt: new Date(user.createdAt),
        }));

        // 确保营销卡片字段存在
        if (!parsed.marketingCards) {
          parsed.marketingCards = [];
        } else {
          parsed.marketingCards = parsed.marketingCards.map((card: MarketingCard) => ({
            ...card,
            createdAt: new Date(card.createdAt),
            updatedAt: new Date(card.updatedAt),
          }));
        }

        return parsed;
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }

    return { users: [], marketingCards: [] };
  }

  // 保存数据到本地存储
  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(DB_KEY, JSON.stringify(this.data));
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  // 创建虚拟用户（使用随机用户名）
  async createUsers(domain: string, count: number): Promise<VirtualUser[]> {
    const newUsers: VirtualUser[] = [];
    const maxAttempts = count * 3; // 最多尝试次数，避免无限循环
    let attempts = 0;

    while (newUsers.length < count && attempts < maxAttempts) {
      attempts++;

      // 生成随机用户名
      const username = generateRandomUsername();
      const email = `${username}@${domain}`;

      // 检查邮箱是否已存在
      const existingUser = this.data.users.find(user => user.email === email);
      if (existingUser) {
        continue; // 跳过已存在的邮箱，重新生成
      }

      const user: VirtualUser = {
        id: generateId(),
        email,
        password: generateRandomPassword(8),
        createdAt: new Date(),
        isActive: false, // 默认未激活
      };

      newUsers.push(user);
      this.data.users.push(user);
    }

    this.saveToStorage();
    return newUsers;
  }

  // 获取所有用户
  async getAllUsers(): Promise<VirtualUser[]> {
    return [...this.data.users];
  }

  // 根据邮箱获取用户
  async getUserByEmail(email: string): Promise<VirtualUser | null> {
    return this.data.users.find(user => user.email === email) || null;
  }

  // 根据ID获取用户
  async getUserById(id: string): Promise<VirtualUser | null> {
    return this.data.users.find(user => user.id === id) || null;
  }

  // 验证用户登录
  async validateUser(email: string, password: string): Promise<VirtualUser | null> {
    const user = await this.getUserByEmail(email);
    
    if (!user || !user.isActive) {
      return null;
    }

    if (user.password === password) {
      return user;
    }

    return null;
  }

  // 删除用户
  async deleteUser(id: string): Promise<boolean> {
    const index = this.data.users.findIndex(user => user.id === id);
    
    if (index === -1) {
      return false;
    }

    this.data.users.splice(index, 1);
    this.saveToStorage();
    return true;
  }

  // 切换用户状态
  async toggleUserStatus(id: string): Promise<VirtualUser | null> {
    const user = this.data.users.find(user => user.id === id);

    if (!user) {
      return null;
    }

    user.isActive = !user.isActive;

    // 如果是激活操作，记录激活时间
    if (user.isActive) {
      user.activatedAt = new Date();
    }

    this.saveToStorage();
    return user;
  }

  // 更新用户信息
  async updateUser(id: string, updates: Partial<VirtualUser>): Promise<VirtualUser | null> {
    const user = this.data.users.find(user => user.id === id);
    
    if (!user) {
      return null;
    }

    Object.assign(user, updates);
    this.saveToStorage();
    return user;
  }

  // 获取统计信息
  async getStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    domains: string[];
  }> {
    const totalUsers = this.data.users.length;
    const activeUsers = this.data.users.filter(user => user.isActive).length;
    const domains = [...new Set(this.data.users.map(user => user.email.split('@')[1]))];

    return {
      totalUsers,
      activeUsers,
      domains,
    };
  }

  // 清空所有数据
  async clearAll(): Promise<void> {
    this.data = { users: [], marketingCards: [] };
    this.saveToStorage();
  }

  // 导出数据
  async exportData(): Promise<string> {
    return JSON.stringify(this.data, null, 2);
  }

  // 导入数据
  async importData(jsonData: string): Promise<boolean> {
    try {
      const imported = JSON.parse(jsonData);
      
      // 验证数据格式
      if (!imported.users || !Array.isArray(imported.users)) {
        throw new Error('无效的数据格式');
      }

      // 验证用户数据
      for (const user of imported.users as VirtualUser[]) {
        if (!user.id || !user.email || !user.password) {
          throw new Error('用户数据不完整');
        }
      }

      this.data = {
        users: imported.users.map((user: VirtualUser) => ({
          ...user,
          createdAt: new Date(user.createdAt),
        })),
        marketingCards: imported.marketingCards?.map((card: MarketingCard) => ({
          ...card,
          createdAt: new Date(card.createdAt),
          updatedAt: new Date(card.updatedAt),
        })) || [],
      };

      this.saveToStorage();
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }

  // ==================== 营销卡片管理 ====================

  // 获取所有营销卡片
  async getAllMarketingCards(): Promise<MarketingCard[]> {
    return [...this.data.marketingCards].sort((a, b) => a.order - b.order);
  }

  // 获取激活的营销卡片
  async getActiveMarketingCards(): Promise<MarketingCard[]> {
    return this.data.marketingCards
      .filter(card => card.isActive)
      .sort((a, b) => a.order - b.order);
  }

  // 根据ID获取营销卡片
  async getMarketingCardById(id: string): Promise<MarketingCard | null> {
    return this.data.marketingCards.find(card => card.id === id) || null;
  }

  // 创建营销卡片
  async createMarketingCard(cardData: Omit<MarketingCard, 'id' | 'createdAt' | 'updatedAt'>): Promise<MarketingCard> {
    const now = new Date();
    const newCard: MarketingCard = {
      id: generateId(),
      ...cardData,
      createdAt: now,
      updatedAt: now,
    };

    this.data.marketingCards.push(newCard);
    this.saveToStorage();

    return newCard;
  }

  // 更新营销卡片
  async updateMarketingCard(id: string, updates: Partial<Omit<MarketingCard, 'id' | 'createdAt'>>): Promise<MarketingCard | null> {
    const card = this.data.marketingCards.find(c => c.id === id);

    if (!card) {
      return null;
    }

    Object.assign(card, updates, { updatedAt: new Date() });
    this.saveToStorage();

    return card;
  }

  // 删除营销卡片
  async deleteMarketingCard(id: string): Promise<boolean> {
    const index = this.data.marketingCards.findIndex(card => card.id === id);

    if (index === -1) {
      return false;
    }

    this.data.marketingCards.splice(index, 1);
    this.saveToStorage();

    return true;
  }

  // 初始化默认营销卡片
  async initializeDefaultMarketingCards(): Promise<void> {
    if (this.data.marketingCards.length === 0) {
      const defaultCard: MarketingCard = {
        id: generateId(),
        title: '🚀 LemooMail - 轻量级邮件管理系统',
        description: '基于 Cloudflare Catch-All 转发的虚拟邮箱解决方案，零服务器成本，现代化技术栈，完美支持域名邮箱管理。无需复杂配置，一键部署即可使用。',
        imageUrl: '',
        buttonText: '立即开始使用',
        buttonLink: '/login',
        isActive: true,
        order: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.data.marketingCards.push(defaultCard);
      this.saveToStorage();

      console.log('默认营销卡片初始化完成');
    }
  }

  // 初始化示例数据（仅初始化营销卡片，不创建测试用户）
  async initSampleData(): Promise<void> {
    // 只初始化默认营销卡片，不创建测试用户
    await this.initializeDefaultMarketingCards();

    console.log('默认配置初始化完成');
  }
}

// 创建数据库服务实例
export const db = new DatabaseService();

// 在客户端初始化示例数据
if (typeof window !== 'undefined') {
  db.initSampleData().catch(console.error);
}
