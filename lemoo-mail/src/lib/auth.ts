import type { VirtualUser } from '@/types';

// 认证工具类
export class AuthUtils {
  // 验证token格式
  static isValidToken(token: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString();
      const parts = decoded.split(':');
      return parts.length === 2 && <PERSON><PERSON><PERSON>(parts[0]) && <PERSON><PERSON>an(parts[1]);
    } catch {
      return false;
    }
  }

  // 从token中提取用户ID
  static getUserIdFromToken(token: string): string | null {
    try {
      const decoded = Buffer.from(token, 'base64').toString();
      const parts = decoded.split(':');
      return parts[0] || null;
    } catch {
      return null;
    }
  }

  // 检查token是否过期（24小时）
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString();
      const parts = decoded.split(':');
      const timestamp = parseInt(parts[1]);
      const now = Date.now();
      const expiry = 24 * 60 * 60 * 1000; // 24小时
      
      return (now - timestamp) > expiry;
    } catch {
      return true;
    }
  }

  // 生成新token
  static generateToken(userId: string): string {
    return Buffer.from(`${userId}:${Date.now()}`).toString('base64');
  }
}

// 认证状态管理
export class AuthManager {
  private static instance: AuthManager;
  private currentUser: VirtualUser | null = null;
  private token: string | null = null;

  private constructor() {
    // 从localStorage恢复认证状态
    this.restoreAuthState();
  }

  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  // 从localStorage恢复认证状态
  private restoreAuthState(): void {
    if (typeof window === 'undefined') return;

    try {
      const storedAuth = localStorage.getItem('auth-storage');
      if (storedAuth) {
        const parsed = JSON.parse(storedAuth);
        if (parsed.state?.user && parsed.state?.token) {
          this.currentUser = parsed.state.user;
          this.token = parsed.state.token;
        }
      }
    } catch (error) {
      console.error('恢复认证状态失败:', error);
    }
  }

  // 设置认证状态
  setAuth(user: VirtualUser, token: string): void {
    this.currentUser = user;
    this.token = token;
  }

  // 清除认证状态
  clearAuth(): void {
    this.currentUser = null;
    this.token = null;
  }

  // 获取当前用户
  getCurrentUser(): VirtualUser | null {
    return this.currentUser;
  }

  // 获取当前token
  getToken(): string | null {
    return this.token;
  }

  // 检查是否已认证
  isAuthenticated(): boolean {
    if (!this.currentUser || !this.token) {
      return false;
    }

    // 检查token是否有效
    if (!AuthUtils.isValidToken(this.token) || AuthUtils.isTokenExpired(this.token)) {
      this.clearAuth();
      return false;
    }

    return true;
  }

  // 检查是否为管理员
  isAdmin(): boolean {
    // 简单的管理员检查：邮箱包含admin或以admin开头
    if (!this.currentUser) return false;
    
    const email = this.currentUser.email.toLowerCase();
    return email.includes('admin') || email.startsWith('admin@');
  }
}

// 路由保护Hook
export function useAuthGuard() {
  const authManager = AuthManager.getInstance();

  const requireAuth = (callback?: () => void) => {
    if (!authManager.isAuthenticated()) {
      // 重定向到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      return false;
    }
    
    if (callback) {
      callback();
    }
    return true;
  };

  const requireAdmin = (callback?: () => void) => {
    if (!authManager.isAuthenticated()) {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      return false;
    }

    if (!authManager.isAdmin()) {
      // 重定向到普通用户页面
      if (typeof window !== 'undefined') {
        window.location.href = '/mail';
      }
      return false;
    }

    if (callback) {
      callback();
    }
    return true;
  };

  return {
    requireAuth,
    requireAdmin,
    isAuthenticated: authManager.isAuthenticated(),
    isAdmin: authManager.isAdmin(),
    currentUser: authManager.getCurrentUser(),
  };
}

// 请求拦截器（为API请求添加认证头）
export function createAuthenticatedFetch() {
  const authManager = AuthManager.getInstance();

  return async (url: string, options: RequestInit = {}) => {
    const token = authManager.getToken();
    
    if (token) {
      options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
      };
    }

    return fetch(url, options);
  };
}

// 导出认证管理器实例
export const authManager = AuthManager.getInstance();
