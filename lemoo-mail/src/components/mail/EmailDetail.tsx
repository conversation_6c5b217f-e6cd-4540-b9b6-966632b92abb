'use client';

import { useMailStore } from '@/store/mail';
import { formatDate } from '@/lib/utils';
import { Button } from '@/components/ui/button';

export default function EmailDetail() {
  const { 
    selectedEmail, 
    markAsRead, 
    markAsUnread, 
    toggleStar, 
    deleteEmail 
  } = useMailStore();

  if (!selectedEmail) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-6xl mb-4">📧</div>
          <p className="text-gray-500 text-lg mb-2">选择一封邮件来查看</p>
          <p className="text-gray-400 text-sm">
            从左侧邮件列表中点击任意邮件
          </p>
        </div>
      </div>
    );
  }

  const handleStarToggle = () => {
    toggleStar(selectedEmail.id);
  };

  const handleMarkAsRead = () => {
    markAsRead(selectedEmail.id);
  };

  const handleMarkAsUnread = () => {
    markAsUnread(selectedEmail.id);
  };

  const handleDelete = () => {
    if (confirm('确定要删除这封邮件吗？')) {
      deleteEmail(selectedEmail.id);
    }
  };

  const handleReply = () => {
    // TODO: 实现回复功能
    alert('回复功能暂未实现');
  };

  const handleForward = () => {
    // TODO: 实现转发功能
    alert('转发功能暂未实现');
  };

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* 邮件头部 */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {selectedEmail.subject || '(无主题)'}
            </h1>
            
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="font-medium w-12">发件人:</span>
                <span>{selectedEmail.from}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium w-12">收件人:</span>
                <span>{selectedEmail.to}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium w-12">时间:</span>
                <span>{selectedEmail.date.toLocaleString('zh-CN')}</span>
              </div>
            </div>
          </div>

          {/* 状态标识 */}
          <div className="flex items-center space-x-2 ml-4">
            {!selectedEmail.isRead && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                未读
              </span>
            )}
            {selectedEmail.isStarred && (
              <span className="text-yellow-500">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </span>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleReply}
            size="sm"
            variant="outline"
          >
            回复
          </Button>
          
          <Button
            onClick={handleForward}
            size="sm"
            variant="outline"
          >
            转发
          </Button>

          <Button
            onClick={handleStarToggle}
            size="sm"
            variant="outline"
            className={selectedEmail.isStarred ? 'text-yellow-600' : ''}
          >
            {selectedEmail.isStarred ? '取消标星' : '标星'}
          </Button>

          {selectedEmail.isRead ? (
            <Button
              onClick={handleMarkAsUnread}
              size="sm"
              variant="outline"
            >
              标为未读
            </Button>
          ) : (
            <Button
              onClick={handleMarkAsRead}
              size="sm"
              variant="outline"
            >
              标为已读
            </Button>
          )}

          <Button
            onClick={handleDelete}
            size="sm"
            variant="destructive"
          >
            删除
          </Button>
        </div>
      </div>

      {/* 邮件内容 */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="prose max-w-none">
          {/* 简单的邮件内容渲染 */}
          <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
            {selectedEmail.body}
          </div>
        </div>

        {/* 附件（如果有） */}
        {selectedEmail.attachments && selectedEmail.attachments.length > 0 && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-3">
              附件 ({selectedEmail.attachments.length})
            </h3>
            <div className="space-y-2">
              {selectedEmail.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {attachment.filename}
                      </p>
                      <p className="text-xs text-gray-500">
                        {attachment.size} bytes
                      </p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    下载
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
