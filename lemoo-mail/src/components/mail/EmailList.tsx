'use client';

import { useMailStore } from '@/store/mail';
import { formatDate, getEmailPreview } from '@/lib/utils';
import type { Email } from '@/types';

interface EmailListProps {
  userEmail: string;
}

export default function EmailList({ userEmail }: EmailListProps) {
  const { 
    emails, 
    selectedEmail, 
    isLoading, 
    error, 
    currentFolder,
    searchQuery,
    selectEmail,
    markAsRead,
    markAsUnread,
    toggleStar 
  } = useMailStore();

  // 筛选邮件
  const filteredEmails = emails.filter(email => {
    // 搜索筛选
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        email.subject.toLowerCase().includes(query) ||
        email.from.toLowerCase().includes(query) ||
        email.body.toLowerCase().includes(query)
      );
    }

    // 文件夹筛选
    switch (currentFolder) {
      case 'inbox':
        return true; // 显示所有邮件
      case 'starred':
        return email.isStarred;
      case 'unread':
        return !email.isRead;
      default:
        return true;
    }
  });

  const handleEmailClick = (email: Email) => {
    selectEmail(email);
  };

  const handleStarClick = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    toggleStar(emailId);
  };

  const handleMarkAsRead = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    markAsRead(emailId);
  };

  const handleMarkAsUnread = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    markAsUnread(emailId);
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-500">加载邮件中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-2">加载邮件失败</p>
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (filteredEmails.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📭</div>
          <p className="text-gray-500 mb-2">
            {searchQuery ? '没有找到匹配的邮件' : '暂无邮件'}
          </p>
          {searchQuery && (
            <p className="text-gray-400 text-sm">
              尝试使用不同的关键词搜索
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div className="h-full overflow-y-auto">
        <div className="divide-y divide-gray-200">
          {filteredEmails.map((email) => (
            <div
              key={email.id}
              className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                selectedEmail?.id === email.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
              } ${!email.isRead ? 'bg-blue-25' : ''}`}
              onClick={() => handleEmailClick(email)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {/* 发件人和时间 */}
                  <div className="flex items-center justify-between mb-1">
                    <p className={`text-sm truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>
                      {email.from}
                    </p>
                    <div className="flex items-center space-x-2 ml-2">
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {formatDate(email.date)}
                      </span>
                      
                      {/* 星标按钮 */}
                      <button
                        onClick={(e) => handleStarClick(e, email.id)}
                        className={`p-1 rounded hover:bg-gray-200 ${
                          email.isStarred ? 'text-yellow-500' : 'text-gray-400'
                        }`}
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* 主题 */}
                  <p className={`text-sm mb-1 truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-800'}`}>
                    {email.subject || '(无主题)'}
                  </p>

                  {/* 邮件预览 */}
                  <p className="text-xs text-gray-500 line-clamp-2">
                    {getEmailPreview(email.body, 120)}
                  </p>
                </div>

                {/* 未读标识 */}
                {!email.isRead && (
                  <div className="ml-2 flex-shrink-0">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                )}
              </div>

              {/* 操作按钮（悬停时显示） */}
              <div className="flex items-center justify-end space-x-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                {email.isRead ? (
                  <button
                    onClick={(e) => handleMarkAsUnread(e, email.id)}
                    className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-200"
                  >
                    标为未读
                  </button>
                ) : (
                  <button
                    onClick={(e) => handleMarkAsRead(e, email.id)}
                    className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-200"
                  >
                    标为已读
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
