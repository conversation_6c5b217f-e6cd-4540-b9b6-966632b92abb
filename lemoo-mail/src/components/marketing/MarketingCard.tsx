'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { MarketingCard as MarketingCardType } from '@/types';
import { useRouter } from 'next/navigation';

interface MarketingCardProps {
  card: MarketingCardType;
}

export default function MarketingCard({ card }: MarketingCardProps) {
  const router = useRouter();

  const handleButtonClick = () => {
    if (card.buttonLink.startsWith('/')) {
      router.push(card.buttonLink);
    } else {
      window.open(card.buttonLink, '_blank');
    }
  };

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {card.imageUrl && (
        <div className="aspect-video overflow-hidden">
          <img
            src={card.imageUrl}
            alt={card.title}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
            loading="lazy"
          />
        </div>
      )}
      <CardContent className="p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {card.title}
        </h2>
        <p className="text-gray-600 mb-6 leading-relaxed">
          {card.description}
        </p>
        <Button
          onClick={handleButtonClick}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
        >
          {card.buttonText}
        </Button>
      </CardContent>
    </Card>
  );
}
