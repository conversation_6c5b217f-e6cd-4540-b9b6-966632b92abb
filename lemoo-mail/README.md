# LemooMail - 轻量级邮件管理系统

一个基于 Next.js 的轻量级邮件管理系统，利用 Cloudflare Catch-All 转发功能实现虚拟邮箱管理。

## ✨ 特性

- 🚀 **零服务器成本** - 基于 Netlify 静态部署
- 📧 **Catch-All 转发** - 利用 Cloudflare 邮件转发，无需邮件服务器
- 👥 **用户管理** - 批量创建域名邮箱账户
- 🔐 **安全认证** - 自动生成随机密码
- 📱 **响应式设计** - 支持桌面和移动设备
- ⚡ **现代化技术栈** - Next.js 15 + TypeScript + Tailwind CSS

## 🛠 技术栈

- **前端框架**: Next.js 15 (App Router)
- **类型系统**: TypeScript 5+
- **样式方案**: Tailwind CSS 4
- **状态管理**: Zustand
- **UI 组件**: 自定义组件 + Radix UI 风格
- **数据获取**: React Query (TanStack Query)
- **部署平台**: Netlify (静态部署)

## 📋 系统架构

```
用户发邮件 → Cloudflare Catch-All → 转发到163邮箱 → Next.js应用读取 → 分发给虚拟用户
```

### 核心组件

1. **虚拟用户系统** - 管理域名邮箱账户
2. **邮件获取服务** - 从163邮箱读取邮件
3. **邮件分发逻辑** - 根据收件人分发邮件
4. **Web客户端** - 用户界面和管理面板

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd lemoo-mail
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
# 创建 .env.local 文件
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-163-password-or-auth-code
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
打开 [http://localhost:3000](http://localhost:3000)

## 📧 邮件配置

### Cloudflare 设置

1. 在 Cloudflare 中配置域名的邮件路由
2. 设置 Catch-All 规则，将所有邮件转发到你的163邮箱
3. 配置 MX 记录指向 Cloudflare

### 163邮箱设置

1. 开启 IMAP 服务
2. 生成授权码（用于应用登录）
3. 配置环境变量

## 🎯 使用指南

### 管理员功能

1. **访问管理面板** - `/admin`
2. **批量创建用户**
   - 输入域名（如 example.com）
   - 设置用户数量
   - 自定义用户名前缀
3. **用户管理**
   - 查看所有用户
   - 启用/禁用用户
   - 删除用户
   - 导出用户信息

### 用户功能

1. **登录系统** - `/login`
   - 使用域名邮箱和密码登录
2. **邮件管理**
   - 查看收件箱
   - 搜索邮件
   - 标记已读/未读
   - 标星重要邮件

## 🏗 项目结构

```
lemoo-mail/
├── src/
│   ├── app/                 # Next.js App Router 页面
│   │   ├── admin/          # 管理员面板
│   │   ├── api/            # API 路由
│   │   ├── login/          # 登录页面
│   │   └── mail/           # 邮件客户端
│   ├── components/         # React 组件
│   │   ├── ui/             # 基础 UI 组件
│   │   └── mail/           # 邮件相关组件
│   ├── lib/                # 工具函数和服务
│   ├── store/              # Zustand 状态管理
│   └── types/              # TypeScript 类型定义
├── public/                 # 静态资源
├── netlify.toml           # Netlify 部署配置
└── next.config.ts         # Next.js 配置
```

## 🚀 部署

### Netlify 部署

1. **连接 GitHub 仓库**
2. **配置构建设置**
   - Build command: `npm run build`
   - Publish directory: `out`
3. **设置环境变量**
   - `IMAP_USER`
   - `IMAP_PASSWORD`
4. **部署完成**

### 自定义域名

1. 在 Netlify 中添加自定义域名
2. 配置 DNS 记录
3. 启用 HTTPS

## 🔧 开发

### 添加新功能

1. **创建组件** - 在 `src/components/` 中添加新组件
2. **添加页面** - 在 `src/app/` 中创建新路由
3. **API 接口** - 在 `src/app/api/` 中添加 API 路由
4. **状态管理** - 在 `src/store/` 中管理应用状态

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 组件使用函数式组件 + Hooks

## 🐛 故障排除

### 常见问题

1. **邮件无法获取**
   - 检查163邮箱IMAP设置
   - 验证授权码是否正确
   - 确认环境变量配置

2. **部署失败**
   - 检查 Node.js 版本
   - 确认所有依赖已安装
   - 查看构建日志

3. **登录问题**
   - 确认用户已创建
   - 检查密码是否正确
   - 验证用户状态是否激活

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请创建 Issue 或联系开发团队。


⚠️ 部署前检查清单
运行 node scripts/generate-secrets.js 生成安全配置
在Netlify环境变量中配置所有必需变量
运行 node scripts/security-check.js 验证配置
确认163邮箱授权码已正确设置
测试管理员登录功能
验证速率限制功能
确认普通用户无法访问管理功能