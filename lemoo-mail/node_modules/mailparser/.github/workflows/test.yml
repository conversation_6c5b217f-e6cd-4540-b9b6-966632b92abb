name: Run tests

on:
    push:
    pull_request:

jobs:
    test:
        strategy:
            matrix:
                node: [18.x, 20.x, 21.x]
                os: [ubuntu-latest]
        runs-on: ${{ matrix.os }}
        steps:
            - uses: actions/checkout@v3
            - name: Use Node.js ${{ matrix.node }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node }}
            - run: npm install
            - run: npm test
