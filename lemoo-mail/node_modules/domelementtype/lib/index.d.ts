/** Types of elements found in htmlparser2's DOM */
export declare enum ElementType {
    /** Type for the root element of a document */
    Root = "root",
    /** Type for Text */
    Text = "text",
    /** Type for <? ... ?> */
    Directive = "directive",
    /** Type for <!-- ... --> */
    Comment = "comment",
    /** Type for <script> tags */
    Script = "script",
    /** Type for <style> tags */
    Style = "style",
    /** Type for Any tag */
    Tag = "tag",
    /** Type for <![CDATA[ ... ]]> */
    CDATA = "cdata",
    /** Type for <!doctype ...> */
    Doctype = "doctype"
}
/**
 * Tests whether an element is a tag or not.
 *
 * @param elem Element to test
 */
export declare function isTag(elem: {
    type: ElementType;
}): boolean;
/** Type for the root element of a document */
export declare const Root = ElementType.Root;
/** Type for Text */
export declare const Text = ElementType.Text;
/** Type for <? ... ?> */
export declare const Directive = ElementType.Directive;
/** Type for <!-- ... --> */
export declare const Comment = ElementType.Comment;
/** Type for <script> tags */
export declare const Script = ElementType.Script;
/** Type for <style> tags */
export declare const Style = ElementType.Style;
/** Type for Any tag */
export declare const Tag = ElementType.Tag;
/** Type for <![CDATA[ ... ]]> */
export declare const CDATA = ElementType.CDATA;
/** Type for <!doctype ...> */
export declare const Doctype = ElementType.Doctype;
//# sourceMappingURL=index.d.ts.map