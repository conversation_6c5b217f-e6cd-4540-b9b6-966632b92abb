# LemooMail 部署指南

## 🚀 快速部署到 Netlify

### 1. 准备工作

确保你已经完成了项目构建：
```bash
npm run build
```

构建完成后，静态文件将生成在 `out` 目录中。

### 2. Netlify 部署步骤

#### 方法一：拖拽部署（最简单）

1. 访问 [Netlify](https://netlify.com)
2. 登录或注册账户
3. 在仪表板中，直接将 `out` 文件夹拖拽到部署区域
4. 等待部署完成，获得临时域名

#### 方法二：Git 集成部署（推荐）

1. 将代码推送到 GitHub 仓库
2. 在 Netlify 中点击 "New site from Git"
3. 选择你的 GitHub 仓库
4. 配置构建设置：
   - **Build command**: `npm run build`
   - **Publish directory**: `out`
   - **Node version**: `18`

### 3. 自定义域名配置

1. 在 Netlify 项目设置中点击 "Domain management"
2. 点击 "Add custom domain"
3. 输入你的域名（如：mail.yourdomain.com）
4. 按照提示配置 DNS 记录

### 4. 🔐 环境变量配置（必需）

**重要：必须配置以下环境变量，否则系统无法正常工作**

在 Netlify 项目设置 → Environment variables 中添加：

```bash
# 管理员认证（必需）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password
ADMIN_SECRET_KEY=your-random-jwt-secret-key

# 163邮箱配置（必需）
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-163-auth-code
```

**安全要求：**
- `ADMIN_PASSWORD`: 使用强密码（至少12位，包含大小写字母、数字、特殊字符）
- `ADMIN_SECRET_KEY`: 使用随机字符串（至少32位）
- `IMAP_PASSWORD`: 使用163邮箱的授权码，不是登录密码

## 📧 Cloudflare 邮件配置

### 1. 设置邮件路由

1. 登录 Cloudflare 控制台
2. 选择你的域名
3. 进入 "Email" → "Email Routing"
4. 启用邮件路由功能
5. 添加 Catch-All 规则：
   - **匹配**: `*@yourdomain.com`
   - **动作**: 转发到 `<EMAIL>`

### 2. 配置 DNS 记录

Cloudflare 会自动添加必要的 MX 记录，通常包括：
```
MX    @    route1.mx.cloudflare.net    (优先级: 10)
MX    @    route2.mx.cloudflare.net    (优先级: 20)
MX    @    route3.mx.cloudflare.net    (优先级: 30)
```

### 3. 163 邮箱设置

1. 登录 163 邮箱
2. 进入设置 → POP3/SMTP/IMAP
3. 开启 IMAP 服务
4. 生成授权码（用于应用访问）

## 🎯 使用指南

### 管理员功能

1. **管理员登录**：`https://your-domain.com/admin-login`
   - 使用环境变量中配置的管理员用户名和密码
   - 登录后会跳转到管理面板

2. **批量创建用户**：
   - 输入域名（如：yourdomain.com）
   - 设置用户数量（1-100）
   - 点击"创建用户"
   - 复制生成的用户信息

### 用户功能

1. **用户登录**：`https://your-domain.com/login`
2. **使用管理员提供的邮箱和密码登录**
3. **查看邮件**：登录后自动跳转到邮件客户端

## 🔧 故障排除

### 常见问题

1. **邮件无法接收**
   - 检查 Cloudflare 邮件路由配置
   - 确认 MX 记录正确设置
   - 验证 163 邮箱转发功能

2. **用户无法登录**
   - 确认用户已在管理面板创建
   - 检查邮箱地址和密码是否正确
   - 验证用户状态是否为"激活"

3. **页面无法访问**
   - 检查 Netlify 部署状态
   - 确认域名 DNS 配置
   - 查看浏览器控制台错误信息

### 调试技巧

1. **查看浏览器控制台**：按 F12 查看错误信息
2. **检查网络请求**：确认资源加载正常
3. **清除浏览器缓存**：强制刷新页面

## 📊 性能优化

### 已实现的优化

- ✅ 静态站点生成（SSG）
- ✅ 代码分割和懒加载
- ✅ 图片优化
- ✅ CSS 压缩
- ✅ JavaScript 压缩

### 建议的优化

1. **CDN 加速**：Netlify 自带全球 CDN
2. **缓存策略**：配置适当的缓存头
3. **监控性能**：使用 Lighthouse 定期检查

## 🔒 安全建议

1. **定期更新依赖**：`npm audit fix`
2. **使用 HTTPS**：Netlify 自动提供 SSL 证书
3. **限制管理员访问**：考虑添加管理员认证
4. **备份数据**：定期导出用户数据

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目 README.md
3. 提交 GitHub Issue
4. 联系技术支持团队

---

🎉 **恭喜！你的 LemooMail 系统已经成功部署！**
